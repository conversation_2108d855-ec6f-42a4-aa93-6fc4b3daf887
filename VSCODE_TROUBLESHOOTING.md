# VS Code Issue Resolution Guide

This guide contains scripts and instructions to help resolve common VS Code issues, particularly those related to:
- Extension host unresponsiveness
- WebGL warnings and GPU stalls
- Service worker conflicts
- Marketplace connection timeouts
- Augment extension request timeouts

## Your Specific Issues

Based on the error logs you provided, you're experiencing several issues:

1. **Extension Host Unresponsiveness**:
   - `Extension host (LocalProcess pid: 5735) is unresponsive`
   - `<PERSON>R<PERSON> [perf] <PERSON><PERSON><PERSON> reported VERY LONG TASK (5949ms)`

2. **WebGL Warnings**:
   - `Automatic fallback to software WebGL has been deprecated`
   - `GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels`

3. **Service Worker Controller Issues**:
   - `Found unexpected service worker controller`
   - `An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing`

4. **Marketplace Connection Timeouts**:
   - `Error while getting the latest version for the extension ... from https://marketplace.visualstudio.com/`

5. **Augment Extension Request Timeouts**:
   - `Uncaught (in promise) Error: Request timed out: chat-mode-changed`
   - `Uncaught (in promise) Error: Request timed out: agent-set-current-conversation`
   - `Uncaught (in promise) Error: Request timed out: get-remote-agent-notification-enabled-request`
   - `Uncaught (in promise) Error: Request timed out: update-shared-webview-state`
   - `Uncaught (in promise) Error: Request timed out: set-has-ever-used-agent`
   - `Uncaught (in promise) Error: Request timed out: check-agent-auto-mode-approval`

## Scripts Included

### 1. `reset_vscode.sh`
Resets VS Code caches and configures settings to improve performance.

```bash
chmod +x reset_vscode.sh
./reset_vscode.sh
```

### 2. `check_vscode_connectivity.sh`
Checks connectivity to VS Code marketplace and related services.

```bash
chmod +x check_vscode_connectivity.sh
./check_vscode_connectivity.sh
```

### 3. `fix_service_workers.sh`
Fixes service worker issues by clearing caches and creating a clean environment.

```bash
chmod +x fix_service_workers.sh
./fix_service_workers.sh
```

### 4. `fix_augment_extension.sh`
Addresses specific issues with the Augment extension.

```bash
chmod +x fix_augment_extension.sh
./fix_augment_extension.sh
```

## Recommended Approach

1. **First, close VS Code completely**
   ```bash
   killall code
   ```

2. **Run the connectivity check to identify network issues**
   ```bash
   ./check_vscode_connectivity.sh
   ```

3. **Reset VS Code caches and settings**
   ```bash
   ./reset_vscode.sh
   ```

4. **Fix service worker issues**
   ```bash
   ./fix_service_workers.sh
   ```

5. **Fix Augment extension issues**
   ```bash
   ./fix_augment_extension.sh
   ```

6. **Launch VS Code with optimized settings**
   ```bash
   ~/launch_vscode_optimized.sh
   ```

## Alternative Solutions

If the scripts don't resolve your issues, try these additional steps:

1. **Launch VS Code with hardware acceleration disabled**
   ```bash
   code --disable-gpu
   ```

2. **Launch VS Code with service workers disabled**
   ```bash
   code --disable-features=ServiceWorker
   ```

3. **Reinstall VS Code**
   ```bash
   # For Ubuntu/Debian
   sudo apt remove code
   sudo apt purge code
   rm -rf ~/.config/Code
   rm -rf ~/.vscode
   sudo apt install code
   
   # For Fedora/RHEL
   sudo dnf remove code
   rm -rf ~/.config/Code
   rm -rf ~/.vscode
   sudo dnf install code
   ```

4. **Check for system updates, especially graphics drivers**
   ```bash
   # For Ubuntu/Debian
   sudo apt update
   sudo apt upgrade
   
   # For Fedora/RHEL
   sudo dnf update
   ```

## Additional Resources

- [VS Code Troubleshooting Guide](https://code.visualstudio.com/docs/supporting/troubleshoot-terminal-launch)
- [VS Code Performance Issues](https://code.visualstudio.com/docs/supporting/faq#_vs-code-is-blank)
- [VS Code Extension Host](https://code.visualstudio.com/api/advanced-topics/extension-host)
- [WebGL Troubleshooting](https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/WebGL_best_practices)
