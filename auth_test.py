import requests
import json
import time
import random
import string

BASE_URL = "http://127.0.0.1:8000"
session = requests.Session()

def random_string(length=8):
    """Generate a random string of fixed length"""
    letters = string.ascii_lowercase
    return ''.join(random.choice(letters) for i in range(length))

def print_step(step):
    print("\n" + "="*80)
    print(f"STEP: {step}")
    print("="*80)

def print_response(response):
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response Text: {response.text[:500]}...")

# Step 1: Register a new user
print_step("1. Register a new user")
register_data = {
    "email": f"test_{int(time.time())}@example.com",
    "password": "Password123!"
}
print(f"Registration data: {register_data}")

register_response = session.post(
    f"{BASE_URL}/api/auth/register", 
    json=register_data,
    headers={"Content-Type": "application/json"}
)
print_response(register_response)

# Check if we were redirected to login
if register_response.url.endswith("/login"):
    print("Successfully redirected to login page after registration")
else:
    print(f"Unexpected redirect to: {register_response.url}")

# Step 2: Login with the new user
print_step("2. Login with the new user")
login_data = {
    "email": register_data["email"],
    "password": register_data["password"]
}
print(f"Login data: {login_data}")

login_response = session.post(
    f"{BASE_URL}/api/auth/login", 
    json=login_data,
    headers={"Content-Type": "application/json"}
)
print_response(login_response)

# Check if we were redirected to home page
if login_response.url == f"{BASE_URL}/" or login_response.url == f"{BASE_URL}":
    print("Successfully logged in and redirected to home page")
else:
    print(f"Unexpected redirect to: {login_response.url}")

# Step 3: Access the create profile page
print_step("3. Access the create profile page")
profile_page_response = session.get(f"{BASE_URL}/create-profile")
print(f"Status Code: {profile_page_response.status_code}")
print(f"URL: {profile_page_response.url}")

# Step 4: Create a profile
print_step("4. Create a profile")
profile_data = {
    "full_name": "Test User",
    "bio": "This is a test bio for authentication flow testing",
    "location": "Test City",
    "website": "https://example.com"
}
print(f"Profile data: {profile_data}")

# Note: In a real test, we would also include a profile picture
profile_response = session.post(f"{BASE_URL}/create-profile", data=profile_data)
print(f"Status Code: {profile_response.status_code}")
print(f"URL: {profile_response.url}")

# Step 5: Access the create post page
print_step("5. Access the create post page")
create_post_response = session.get(f"{BASE_URL}/create-post")
print(f"Status Code: {create_post_response.status_code}")
print(f"URL: {create_post_response.url}")

# Step 6: Create a post
print_step("6. Create a post")
post_data = {
    "title": "Test Post",
    "content": "<p>This is a test post content for authentication flow testing</p>"
}
print(f"Post data: {post_data}")

post_response = session.post(f"{BASE_URL}/create-post", data=post_data)
print(f"Status Code: {post_response.status_code}")
print(f"URL: {post_response.url}")

# Step 7: Test Login with incorrect password
print_step("7. Test Login with Incorrect Password")
incorrect_login_data = {
    "email": register_data["email"],
    "password": "WrongPassword123!"
}
print(f"Incorrect login data: {incorrect_login_data}")

incorrect_login_response = requests.post(
    f"{BASE_URL}/api/auth/login", 
    json=incorrect_login_data,
    headers={"Content-Type": "application/json"}
)
print_response(incorrect_login_response)

# Step 8: Test Registration with existing email
print_step("8. Test Registration with Existing Email")
duplicate_register_data = {
    "email": register_data["email"],  # Same email as before
    "password": "Password123!"
}
print(f"Duplicate registration data: {duplicate_register_data}")

duplicate_register_response = requests.post(
    f"{BASE_URL}/api/auth/register", 
    json=duplicate_register_data,
    headers={"Content-Type": "application/json"}
)
print_response(duplicate_register_response)

# Step 9: Test Registration with mismatched passwords
print_step("9. Test Registration with Mismatched Passwords")
mismatched_register_data = {
    "username": f"another_testuser_{random_string()}",
    "email": f"another_test_{random_string()}@example.com",
    "password": "Password123!",
    "confirm_password": "DifferentPassword123!"
}
print(f"Mismatched password registration data: {mismatched_register_data}")

mismatched_register_response = requests.post(f"{BASE_URL}/register", data=mismatched_register_data)
print_response(mismatched_register_response)

# Step 10: Test Logout
print_step("10. Test Logout")
logout_response = session.get(f"{BASE_URL}/logout")
print(f"Status Code: {logout_response.status_code}")
print(f"URL: {logout_response.url}")

# Step 11: Try to access protected page after logout
print_step("11. Try to access protected page after logout")
protected_page_response = session.get(f"{BASE_URL}/create-post")
print(f"Status Code: {protected_page_response.status_code}")
print(f"URL: {protected_page_response.url}")

# Step 12: Test WebAuthn Support
print_step("12. Test WebAuthn capability")
webauthn_support_response = session.get(f"{BASE_URL}/api/auth/webauthn/supported")
print_response(webauthn_support_response)

if webauthn_support_response.status_code == 200:
    # Step 13: Register WebAuthn credentials
    print_step("13. Register WebAuthn credentials")
    register_data = {
        "email": register_data["email"]
    }
    webauthn_reg_response = session.post(
        f"{BASE_URL}/api/auth/webauthn/register",
        json=register_data,
        headers={"Content-Type": "application/json"}
    )
    print_response(webauthn_reg_response)

# Final check - cleanup
print_step("14. Final Cleanup")
print("\nAuthentication testing completed!")
