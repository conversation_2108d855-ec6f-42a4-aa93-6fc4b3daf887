"""Create adverts table

Revision ID: create_adverts_table
Revises: create_events_table
Create Date: 2025-05-16 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'create_adverts_table'
down_revision = 'create_events_table'
branch_labels = None
depends_on = None

def upgrade():
    op.create_table('adverts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('title', sa.String(length=200), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('link', sa.String(length=500), nullable=True),
        sa.Column('link_text', sa.String(length=100), nullable=True),
        sa.Column('expiry_date', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_adverts_id'), 'adverts', ['id'], unique=False)

def downgrade():
    op.drop_index(op.f('ix_adverts_id'), table_name='adverts')
    op.drop_table('adverts')
