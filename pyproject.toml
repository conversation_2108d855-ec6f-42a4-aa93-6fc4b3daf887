[tool.poetry]
name = "davartruth"
version = "0.1.0"
description = "A faith-based application with blog and real-time chat features"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.0"
uvicorn = "^0.23.2"
sqlalchemy = "^2.0.22"
aiosqlite = "^0.19.0"
jinja2 = "^3.1.2"
python-multipart = "^0.0.6"
aiofiles = "^23.2.1"
websockets = "^11.0.3"
passlib = "^1.7.4"
python-jose = "^3.3.0"
bcrypt = "^4.0.1"
redis = "^6.0.0"

[tool.poetry.dev-dependencies]
pytest = "^7.4.2"
black = "^23.9.1"
flake8 = "^6.1.0"
isort = "^5.12.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3