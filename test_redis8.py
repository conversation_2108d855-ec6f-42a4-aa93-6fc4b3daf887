#!/usr/bin/env python3
"""
Test script to verify Redis 8 integration works correctly.
"""

import asyncio
import json
from app.redis_store import (
    redis_client, 
    set_session_data, 
    get_session_data, 
    delete_session, 
    set_hash_with_expiry,
    get_hash_and_delete,
    get_hash_with_expiry
)

async def test_redis_connection():
    """Test basic Redis connection."""
    try:
        # Test ping
        result = await redis_client.ping()
        print(f"Redis connection test (ping): {'SUCCESS' if result else 'FAILED'}")
        
        # Test basic set/get
        await redis_client.set("test:key", "test_value")
        value = await redis_client.get("test:key")
        if isinstance(value, bytes):
            value = value.decode('utf-8')
        print(f"Redis basic set/get test: {'SUCCESS' if value == 'test_value' else 'FAILED'}")
        
        # Clean up
        await redis_client.delete("test:key")
        return True
    except Exception as e:
        print(f"Redis connection test failed: {e}")
        return False

async def test_session_functions():
    """Test session-related functions."""
    try:
        # Test session data
        session_id = "test_session_id"
        await set_session_data(session_id, "test_key", "test_value")
        value = await get_session_data(session_id, "test_key")
        print(f"Session data test: {'SUCCESS' if value == 'test_value' else 'FAILED'}")
        
        # Clean up
        await delete_session(session_id)
        return True
    except Exception as e:
        print(f"Session functions test failed: {e}")
        return False

async def test_redis8_features():
    """Test Redis 8 specific features."""
    try:
        # Test hash with expiry (HSETEX)
        hash_key = "test:hash"
        await set_hash_with_expiry(hash_key, "field1", "value1", 60)
        
        # Test get hash with expiry (HGETEX)
        value = await get_hash_with_expiry(hash_key, "field1", 120)
        print(f"HSETEX/HGETEX test: {'SUCCESS' if value == 'value1' else 'FAILED'}")
        
        # Test get hash and delete (HGETDEL)
        await set_hash_with_expiry(hash_key, "field2", "value2", 60)
        value = await get_hash_and_delete(hash_key, "field2")
        print(f"HGETDEL test: {'SUCCESS' if value == 'value2' else 'FAILED'}")
        
        # Verify field2 is deleted
        value = await redis_client.hget(hash_key, "field2")
        print(f"HGETDEL verification: {'SUCCESS' if value is None else 'FAILED'}")
        
        # Clean up
        await redis_client.delete(hash_key)
        return True
    except Exception as e:
        print(f"Redis 8 features test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("Testing Redis 8 integration...")
    
    # Test Redis connection
    connection_ok = await test_redis_connection()
    if not connection_ok:
        print("Redis connection failed. Make sure Redis 8 is running.")
        return
    
    # Test session functions
    await test_session_functions()
    
    # Test Redis 8 features
    await test_redis8_features()
    
    print("All tests completed.")

if __name__ == "__main__":
    asyncio.run(main())
