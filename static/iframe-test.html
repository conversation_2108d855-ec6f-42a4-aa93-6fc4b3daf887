<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iframe DOCTYPE Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #5c0e14;
        }
        .iframe-container {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        iframe {
            width: 100%;
            height: 150px;
            border: 1px solid #ccc;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .standards {
            background-color: #d4edda;
            border-left: 5px solid #28a745;
        }
        .quirks {
            background-color: #f8d7da;
            border-left: 5px solid #dc3545;
        }
        .info {
            background-color: #e8f4f8;
            padding: 15px;
            border-left: 4px solid #5c0e14;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Iframe DOCTYPE Test</h1>
        
        <div class="info">
            <p>This page tests whether our DOCTYPE enforcer is working correctly with iframes.</p>
            <p>Check the browser console for detailed information about document modes.</p>
        </div>
        
        <div class="iframe-container">
            <h3>Iframe with DOCTYPE</h3>
            <iframe id="iframe-with-doctype" srcdoc="<!DOCTYPE html><html><head><title>With DOCTYPE</title></head><body><p>This iframe has a DOCTYPE declaration.</p></body></html>"></iframe>
            <div id="result-1" class="result">Checking mode...</div>
        </div>
        
        <div class="iframe-container">
            <h3>Iframe without DOCTYPE</h3>
            <iframe id="iframe-without-doctype" srcdoc="<html><head><title>Without DOCTYPE</title></head><body><p>This iframe does NOT have a DOCTYPE declaration.</p></body></html>"></iframe>
            <div id="result-2" class="result">Checking mode...</div>
        </div>
        
        <div class="iframe-container">
            <h3>Iframe with URL Source</h3>
            <iframe id="iframe-url" src="/static/doctype-test.html"></iframe>
            <div id="result-3" class="result">Checking mode...</div>
        </div>
    </div>
    
    <script>
        // Function to check iframe mode
        function checkIframeMode(iframe, resultElement) {
            iframe.addEventListener('load', function() {
                try {
                    const iframeDoc = iframe.contentDocument;
                    const mode = iframeDoc.compatMode;
                    
                    const resultDiv = document.getElementById(resultElement);
                    
                    if (mode === 'CSS1Compat') {
                        resultDiv.className = 'result standards';
                        resultDiv.innerHTML = '<strong>Standards Mode (CSS1Compat)</strong>';
                    } else {
                        resultDiv.className = 'result quirks';
                        resultDiv.innerHTML = '<strong>Quirks Mode (BackCompat)</strong>';
                    }
                } catch (e) {
                    console.error(`Error checking iframe mode:`, e);
                    document.getElementById(resultElement).innerHTML = 'Error: ' + e.message;
                }
            });
        }
        
        // Check all iframes
        window.addEventListener('load', function() {
            checkIframeMode(document.getElementById('iframe-with-doctype'), 'result-1');
            checkIframeMode(document.getElementById('iframe-without-doctype'), 'result-2');
            checkIframeMode(document.getElementById('iframe-url'), 'result-3');
        });
    </script>
    
    <!-- Include our DOCTYPE enforcer script -->
    <script src="/static/js/doctype-enforcer.js"></script>
</body>
</html>
