/* Combined CSS for blog page */

/* Import from blog.css */
.blog-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.blog-header {
    text-align: center;
    margin-bottom: 2rem;
}

.blog-title {
    font-size: 2.5rem;
    color: var(--primary-custom);
    margin-bottom: 0.5rem;
}

.blog-subtitle {
    font-size: 1.2rem;
    color: #666;
}

.blog-content {
    display: grid;
    grid-template-columns: 1fr 3fr 1fr;
    gap: 2rem;
}

@media (max-width: 768px) {
    .blog-content {
        grid-template-columns: 1fr;
    }

    .blog-sidebar {
        order: 2;
    }

    .blog-main {
        order: 1;
    }

    .blog-sidebar-right {
        order: 3;
    }
}

.blog-sidebar, .blog-sidebar-right {
    background-color: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.blog-main {
    background-color: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.blog-post {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.blog-post:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.post-title {
    font-size: 1.8rem;
    color: var(--primary-custom);
    margin-bottom: 0.5rem;
}

.post-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.post-author {
    display: flex;
    align-items: center;
    margin-right: 1rem;
}

.post-author-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.post-date {
    margin-left: auto;
}

.post-content {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.post-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.post-action-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.post-action-btn:hover {
    color: var(--primary-custom);
}

.post-action-btn i {
    margin-right: 0.5rem;
}

.sidebar-title {
    font-size: 1.2rem;
    color: var(--primary-custom);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-custom);
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background-color: var(--secondary-custom);
    color: var(--primary-custom);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
    text-decoration: none;
}

.tag:hover {
    background-color: var(--primary-custom);
    color: #fff;
}

.archive-link {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    color: #333;
    text-decoration: none;
    border-bottom: 1px solid #eee;
}

.archive-link:hover {
    color: var(--primary-custom);
}

.archive-count {
    color: #666;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    color: #333;
    text-decoration: none;
}

.pagination-btn:hover {
    background-color: var(--secondary-custom);
}

.pagination-btn.active {
    background-color: var(--primary-custom);
    color: #fff;
    border-color: var(--primary-custom);
}

.pagination-btn.disabled {
    color: #ccc;
    cursor: not-allowed;
}

/* Import from z-index-fix.css */
.dropdown-menu {
    z-index: 50 !important;
}

.modal {
    z-index: 100 !important;
}

.tooltip {
    z-index: 40 !important;
}

.navbar {
    z-index: 30 !important;
}

/* Import from navbar.css - Modified to avoid conflicts with base navbar */
/* These styles are only applied to custom navbars, not the main site navbar */
.custom-navbar-container {
    width: 100%;
    background-color: var(--primary-custom);
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
}

.custom-navbar-brand {
    display: flex;
    align-items: center;
}

.custom-navbar-logo {
    height: 40px;
    margin-right: 0.5rem;
}

.custom-navbar-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
}

.custom-navbar-links {
    display: flex;
    align-items: center;
}

.custom-navbar-link {
    color: white;
    margin-left: 1.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.custom-navbar-link:hover {
    color: var(--secondary-custom);
}

.custom-navbar-mobile-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

@media (max-width: 768px) {
    .custom-navbar-links {
        display: none;
    }

    .custom-navbar-mobile-toggle {
        display: block;
    }

    .custom-navbar-links.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--primary-custom);
        padding: 1rem;
        z-index: 100;
    }

    .custom-navbar-link {
        margin: 0.5rem 0;
    }
}

/* Import from blog-components-fixed.css */
.blog-card {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.blog-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.blog-card-content {
    padding: 1.5rem;
}

.blog-card-title {
    font-size: 1.2rem;
    color: var(--primary-custom);
    margin-bottom: 0.5rem;
}

.blog-card-excerpt {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.blog-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #999;
}

.archive-card {
    background-color: #fff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.archive-card-title {
    font-size: 1.2rem;
    color: var(--primary-custom);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-custom);
}

/* Import from blog-actions.css */
.post-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.post-action-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    color: #495057;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.post-action-btn:hover {
    background-color: #e9ecef;
    color: var(--primary-custom);
}

.post-action-btn i, .post-action-btn svg {
    margin-right: 0.5rem;
}

.comments-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
}

.comment {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #f8f9fa;
}

.comment:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.comment-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 0.75rem;
}

.comment-author {
    font-weight: 600;
    margin-right: 0.5rem;
}

.comment-date {
    font-size: 0.75rem;
    color: #6c757d;
}

.comment-content {
    margin-left: 2.75rem;
    line-height: 1.5;
}

.comment-actions {
    margin-top: 0.5rem;
    margin-left: 2.75rem;
    display: flex;
    gap: 1rem;
}

.comment-action {
    font-size: 0.75rem;
    color: #6c757d;
    cursor: pointer;
}

.comment-action:hover {
    color: var(--primary-custom);
    text-decoration: underline;
}

.comment-form {
    margin-top: 1.5rem;
}

.comment-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    resize: vertical;
    min-height: 100px;
    margin-bottom: 1rem;
}

.comment-submit {
    padding: 0.5rem 1rem;
    background-color: var(--primary-custom);
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.comment-submit:hover {
    background-color: var(--primary-dark);
}

/* Import from blog-footer.css */
.blog-footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: #fff;
    border-top: 1px solid #eee;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 2rem;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-title {
    font-size: 1.2rem;
    color: var(--primary-custom);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--secondary-custom);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-link {
    margin-bottom: 0.5rem;
}

.footer-link a {
    color: #666;
    text-decoration: none;
    transition: color 0.2s;
}

.footer-link a:hover {
    color: var(--primary-custom);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 50%;
    color: #666;
    transition: all 0.2s;
}

.social-icon:hover {
    background-color: var(--primary-custom);
    color: white;
}

.footer-bottom {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

/* Import from modal-fix.css */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.2rem;
    color: var(--primary-custom);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Import from image-fix.css */
img {
    max-width: 100%;
    height: auto;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}

.img-thumbnail {
    padding: 0.25rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    max-width: 100%;
    height: auto;
}

.figure {
    display: inline-block;
}

.figure-img {
    margin-bottom: 0.5rem;
    line-height: 1;
}

.figure-caption {
    font-size: 90%;
    color: #6c757d;
}
