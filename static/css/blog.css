/* Blog-specific styles */

/* Fix for post cards */
.blog-container article {
    margin-bottom: 1.5rem !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    display: block !important;
    float: none !important;
    clear: both !important;
}

/* Fix for images in posts */
.post-content img {
    max-height: 300px !important;
    width: auto !important;
    margin: 1rem auto !important;
    object-fit: contain !important;
    display: block !important;
}

/* Fix for featured post images */
.featured-post-image {
    max-height: 160px !important;
    width: 100% !important;
    object-fit: cover !important;
}

/* Fix for consistent card widths */
.blog-container .card,
.blog-container article,
.blog-container .post-card {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: block !important;
}

/* Navbar styles moved to navbar.css */
/* Ensure accessibility menu is visible in blog */
.blog-page .accessibility-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for blog posts container */
.blog-posts {
    display: block !important;
    width: 100% !important;
    max-width: 768px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    box-sizing: border-box !important;
}

/* Fix for footer affecting layout */
.blog-container + footer {
    clear: both !important;
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    margin-top: 2rem !important;
}

/* Fix for login/register buttons */
.navbar-auth a {
    white-space: nowrap !important;
    display: inline-block !important;
}

/* Navbar styles moved to navbar.css */

/* Fix for post content overflow */
.post-content {
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    hyphens: auto !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Fix for long titles */
.blog-posts article h3 {
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    hyphens: auto !important;
    max-width: 100% !important;
}

/* Fix for share dropdown overflow */
.relative[x-data="{ shareOpen: false }"] .absolute {
    right: 0 !important;
    left: auto !important;
    max-width: 200px !important;
    z-index: 1500 !important;
}

/* Fix for post card borders */
.blog-posts article {
    border-left: 4px solid #5c0e14 !important;
}

.border-b-2.border-primary-custom {
    border-bottom: 2px solid #5c0e14 !important;
}

.border-l-2.border-primary-custom {
    border-left: 2px solid #5c0e14 !important;
}

/* Responsive improvements */
@media (max-width: 640px) {
    .post-actions {
        justify-content: center !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
    }

    .post-actions button, .post-actions .relative {
        padding: 6px !important;
    }

    .featured-post-image {
        max-height: 120px !important;
    }

    /* Improve spacing on mobile */
    .blog-container {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    /* Fix navbar on mobile */
    .navbar-container {
        padding: 0.5rem !important;
    }

    /* Fix footer on mobile */
    .blog-footer {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    /* Fix breadcrumb on mobile */
    .blog-breadcrumb {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    /* Fix share dropdown on mobile */
    .relative[x-data="{ shareOpen: false }"] .absolute {
        right: 0 !important;
        left: auto !important;
        max-width: 180px !important;
    }

    /* Fix comment section overflow on mobile */
    .comment-content, .comment-author {
        max-width: 100% !important;
        overflow-wrap: break-word !important;
        word-wrap: break-word !important;
        word-break: break-word !important;
    }

    /* Fix flex layout issues on mobile */
    .flex.justify-between {
        flex-wrap: wrap !important;
    }

    /* Fix table overflow on mobile */
    .post-content table {
        display: block !important;
        width: 100% !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
}

/* Fix for breadcrumb navigation */
.blog-breadcrumb {
    display: block !important;
    width: 100% !important;
    max-width: 768px !important;
    box-sizing: border-box !important;
    margin-left: auto !important;
    margin-right: auto !important;
    margin-bottom: 1.5rem !important;
}

.blog-breadcrumb > div {
    display: flex !important;
    flex-wrap: wrap !important;
}

.blog-breadcrumb nav {
    flex-shrink: 0 !important;
    margin-bottom: 0.5rem !important;
}

.blog-breadcrumb form {
    flex: 1 1 auto !important;
    min-width: 200px !important;
    margin-bottom: 0.5rem !important;
}

.blog-breadcrumb a[href="/chat"] {
    flex-shrink: 0 !important;
    white-space: nowrap !important;
}

/* Fix for container width consistency */
.container.blog-container {
    max-width: 1024px !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* Newsletter styles */
.newsletter-box {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.newsletter-box h3 {
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.newsletter-input {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    font-size: 1rem;
}

.newsletter-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* Toast Notifications */
.transition {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-200 {
    transition-duration: 200ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.transform {
    transform: translateY(0);
}

.translate-y-0 {
    transform: translateY(0);
}

.translate-y-2 {
    transform: translateY(0.5rem);
}

.opacity-0 {
    opacity: 0;
}

.opacity-100 {
    opacity: 1;
}
