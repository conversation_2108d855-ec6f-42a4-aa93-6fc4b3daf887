/* Unified Navbar Styles */

/* Base navbar container */
nav.bg-primary-custom {
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
    z-index: 1000 !important;
}

/* Main navbar flex container */
nav.bg-primary-custom > div {
    display: flex !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
    box-sizing: border-box !important;
    align-items: center !important;
    justify-content: space-between !important;
}

/* Navbar links container */
#navbar-links {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
}

@media (min-width: 1024px) {
    #navbar-links {
        flex-direction: row !important;
        align-items: center !important;
        justify-content: space-between !important;
        flex-grow: 1 !important;
    }
}

/* Primary navigation */
#navbar-links > div:first-child {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

@media (min-width: 1024px) {
    #navbar-links > div:first-child {
        flex-direction: row !important;
        align-items: center !important;
        margin-right: auto !important;
    }
}

/* Secondary navigation */
#navbar-links > div:nth-child(2) {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

@media (min-width: 1024px) {
    #navbar-links > div:nth-child(2) {
        flex-direction: row !important;
        align-items: center !important;
        margin-left: 2rem !important;
    }
}

/* App Switcher */
.app-switcher {
    display: flex !important;
    margin-bottom: 0.5rem !important;
}

@media (min-width: 1024px) {
    .app-switcher {
        margin-bottom: 0 !important;
    }
}

/* Accessibility Menu */
.accessibility-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1001 !important;
}

@media (max-width: 1023px) {
    .accessibility-menu {
        margin-top: 0.5rem !important;
    }
}

/* Login/Register buttons container */
.navbar-auth {
    display: flex !important;
    margin-top: 1rem !important;
    position: relative !important;
    z-index: 1001 !important;
    flex-shrink: 0 !important;
}

@media (min-width: 1024px) {
    .navbar-auth {
        margin-top: 0 !important;
        margin-left: 1.5rem !important;
    }
}

/* Spacing between navbar items */
.space-x-6 > * + * {
    margin-left: 1.5rem !important;
}

/* Mobile menu styles */
@media (max-width: 1023px) {
    #navbar-links.hidden {
        display: none !important;
    }

    #navbar-links:not(.hidden) {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        align-items: center !important;
        padding: 1.25rem 0 !important;
        gap: 1rem !important;
    }

    /* Show accessibility menu on mobile when navbar is expanded */
    #navbar-links:not(.hidden) .accessibility-menu {
        display: block !important;
        margin-top: 1rem !important;
    }

    /* Ensure login/register buttons are properly contained on mobile */
    .navbar-auth {
        width: auto !important;
        justify-content: center !important;
    }
}

/* Fix for blog-specific navbar issues */
.blog-page nav.bg-primary-custom {
    display: block !important;
    width: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
    z-index: 1000 !important;
}

/* Ensure logo is visible in blog page */
.blog-page .flex.items-center img {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure logo container is properly displayed */
.blog-page nav .flex.items-center {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure accessibility menu is visible in blog page */
.blog-page .relative[x-data="{ open: false }"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for accessibility menu in blog */
.blog-page .accessibility-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1001 !important;
}

.blog-page .navbar-auth {
    position: relative !important;
    z-index: 1001 !important;
    display: flex !important;
}

@media (max-width: 1024px) {
    .blog-page .navbar-auth {
        display: flex !important;
        justify-content: center !important;
        width: auto !important;
        margin-top: 1rem !important;
        flex-shrink: 0 !important;
    }
}

/* Fix for navbar container in blog */
.blog-page .bg-primary-custom > div {
    flex-wrap: nowrap !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Ensure login/register buttons are properly contained */
.blog-page .navbar-auth {
    position: relative !important;
    z-index: 1001 !important;
    display: flex !important;
    flex-shrink: 0 !important;
    margin-left: auto !important;
}

/* Active app indicator */
.app-switcher a.bg-primary-dark {
    position: relative !important;
}

.app-switcher a.bg-primary-dark::after {
    content: '' !important;
    position: absolute !important;
    bottom: -4px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 70% !important;
    height: 3px !important;
    background-color: var(--secondary-custom) !important;
    border-radius: 3px !important;
}
