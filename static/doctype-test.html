<!DOCTYPE html>
<html>
<head>
    <title>DOCTYPE Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #5c0e14;
        }
        .info {
            background-color: #e8f4f8;
            padding: 15px;
            border-left: 4px solid #5c0e14;
            margin: 20px 0;
        }
        .quirks-mode {
            background-color: #ffebee;
            padding: 15px;
            border-left: 4px solid #c62828;
            margin: 20px 0;
        }
        .standards-mode {
            background-color: #e8f5e9;
            padding: 15px;
            border-left: 4px solid #2e7d32;
            margin: 20px 0;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DOCTYPE Test Page</h1>
        
        <div class="info">
            <h2>Current Rendering Mode</h2>
            <p>This page will display which rendering mode your browser is using.</p>
            <div id="mode-display">Checking mode...</div>
        </div>

        <div class="quirks-mode" id="quirks-info" style="display: none;">
            <h2>Quirks Mode Detected</h2>
            <p>Your browser is rendering this page in quirks mode. This means:</p>
            <ul>
                <li>The DOCTYPE declaration is missing or not recognized</li>
                <li>CSS may not be rendered consistently across browsers</li>
                <li>Some modern web features may not work correctly</li>
            </ul>
            <p>Possible solutions:</p>
            <ul>
                <li>Ensure the DOCTYPE declaration is the very first line of the HTML document</li>
                <li>Make sure there are no whitespace characters before the DOCTYPE</li>
                <li>Use the standard HTML5 DOCTYPE: <code>&lt;!DOCTYPE html&gt;</code></li>
                <li>Check that the content-type header is set correctly</li>
            </ul>
        </div>

        <div class="standards-mode" id="standards-info" style="display: none;">
            <h2>Standards Mode Detected</h2>
            <p>Your browser is rendering this page in standards mode. This is good!</p>
            <p>Standards mode ensures that your page will be rendered consistently across modern browsers and that all HTML5 features will work as expected.</p>
        </div>
    </div>

    <script>
        // Check if the browser is in quirks mode or standards mode
        document.addEventListener('DOMContentLoaded', function() {
            var modeDisplay = document.getElementById('mode-display');
            var quirksInfo = document.getElementById('quirks-info');
            var standardsInfo = document.getElementById('standards-info');
            
            if (document.compatMode === 'BackCompat') {
                // Quirks mode
                modeDisplay.textContent = 'This page is being rendered in QUIRKS MODE';
                modeDisplay.style.color = '#c62828';
                modeDisplay.style.fontWeight = 'bold';
                quirksInfo.style.display = 'block';
            } else {
                // Standards mode
                modeDisplay.textContent = 'This page is being rendered in STANDARDS MODE';
                modeDisplay.style.color = '#2e7d32';
                modeDisplay.style.fontWeight = 'bold';
                standardsInfo.style.display = 'block';
            }
        });
    </script>
</body>
</html>
