// Add this to the console to detect which documents are in Quirks Mode
(function() {
    console.log('Main document mode:', document.compatMode);
    
    // Check all iframes
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach((iframe, index) => {
        try {
            const iframeMode = iframe.contentDocument.compatMode;
            console.log(`iframe #${index} mode:`, iframeMode);
            console.log(`iframe #${index} src:`, iframe.src);
            
            // If in quirks mode, add a red border to highlight it
            if (iframeMode === 'BackCompat') {
                iframe.style.border = '3px solid red';
            }
        } catch(e) {
            console.log(`Cannot access iframe #${index} due to same-origin policy`);
        }
    });
})();
