<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quirks Mode Detector</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #5c0e14;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .standards {
            background-color: #d4edda;
            border-left: 5px solid #28a745;
        }
        .quirks {
            background-color: #f8d7da;
            border-left: 5px solid #dc3545;
        }
        .iframe-container {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        iframe {
            width: 100%;
            height: 100px;
            border: 1px solid #ccc;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .debug-info {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 5px solid #6c757d;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Quirks Mode Detector</h1>
        
        <div id="main-result" class="result">
            Checking document mode...
        </div>
        
        <h2>Test Iframes</h2>
        <p>The following iframes will be tested for Quirks Mode:</p>
        
        <div class="iframe-container">
            <h3>Iframe with DOCTYPE</h3>
            <iframe id="iframe-with-doctype" src="about:blank"></iframe>
        </div>
        
        <div class="iframe-container">
            <h3>Iframe without DOCTYPE</h3>
            <iframe id="iframe-without-doctype" src="about:blank"></iframe>
        </div>
        
        <div class="iframe-container">
            <h3>Iframe with blog.html</h3>
            <iframe id="iframe-blog" src="/blog"></iframe>
        </div>
        
        <div class="debug-info" id="debug-info">
            Loading debug information...
        </div>
    </div>
    
    <script>
        // Check main document mode
        document.addEventListener('DOMContentLoaded', function() {
            const mainResult = document.getElementById('main-result');
            const debugInfo = document.getElementById('debug-info');
            
            // Check main document
            if (document.compatMode === 'CSS1Compat') {
                mainResult.classList.add('standards');
                mainResult.innerHTML = '<strong>Main Document:</strong> Standards Mode (CSS1Compat)';
            } else {
                mainResult.classList.add('quirks');
                mainResult.innerHTML = '<strong>Main Document:</strong> Quirks Mode (BackCompat)';
            }
            
            // Create content for iframes
            const iframeWithDoctype = document.getElementById('iframe-with-doctype');
            iframeWithDoctype.onload = function() {
                const doc = iframeWithDoctype.contentDocument;
                doc.open();
                doc.write('<!DOCTYPE html><html><head><title>With DOCTYPE</title></head><body><p>This iframe has a DOCTYPE declaration.</p></body></html>');
                doc.close();
                checkIframeMode(iframeWithDoctype, 'iframe-with-doctype');
            };
            
            const iframeWithoutDoctype = document.getElementById('iframe-without-doctype');
            iframeWithoutDoctype.onload = function() {
                const doc = iframeWithoutDoctype.contentDocument;
                doc.open();
                doc.write('<html><head><title>Without DOCTYPE</title></head><body><p>This iframe does NOT have a DOCTYPE declaration.</p></body></html>');
                doc.close();
                checkIframeMode(iframeWithoutDoctype, 'iframe-without-doctype');
            };
            
            const iframeBlog = document.getElementById('iframe-blog');
            iframeBlog.onload = function() {
                try {
                    checkIframeMode(iframeBlog, 'iframe-blog');
                } catch (e) {
                    console.error('Error checking blog iframe:', e);
                }
            };
            
            // Collect debug information
            setTimeout(function() {
                let debugText = 'DEBUG INFORMATION:\n\n';
                debugText += `User Agent: ${navigator.userAgent}\n`;
                debugText += `Document Mode: ${document.compatMode}\n`;
                debugText += `Document URL: ${document.URL}\n`;
                debugText += `Document Referrer: ${document.referrer}\n\n`;
                
                debugText += 'IFRAME INFORMATION:\n';
                try {
                    debugText += `iframe-with-doctype mode: ${iframeWithDoctype.contentDocument.compatMode}\n`;
                } catch (e) {
                    debugText += `iframe-with-doctype: ${e.message}\n`;
                }
                
                try {
                    debugText += `iframe-without-doctype mode: ${iframeWithoutDoctype.contentDocument.compatMode}\n`;
                } catch (e) {
                    debugText += `iframe-without-doctype: ${e.message}\n`;
                }
                
                try {
                    debugText += `iframe-blog mode: ${iframeBlog.contentDocument.compatMode}\n`;
                    debugText += `iframe-blog URL: ${iframeBlog.contentDocument.URL}\n`;
                } catch (e) {
                    debugText += `iframe-blog: ${e.message}\n`;
                }
                
                debugInfo.textContent = debugText;
            }, 2000);
        });
        
        function checkIframeMode(iframe, id) {
            try {
                const iframeDoc = iframe.contentDocument;
                const iframeMode = iframeDoc.compatMode;
                const container = iframe.parentElement;
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result ' + (iframeMode === 'CSS1Compat' ? 'standards' : 'quirks');
                resultDiv.innerHTML = `<strong>Mode:</strong> ${iframeMode === 'CSS1Compat' ? 'Standards Mode (CSS1Compat)' : 'Quirks Mode (BackCompat)'}`;
                
                container.appendChild(resultDiv);
                
                if (iframeMode === 'BackCompat') {
                    iframe.style.border = '2px solid #dc3545';
                }
            } catch (e) {
                console.error(`Error checking iframe ${id}:`, e);
            }
        }
    </script>
</body>
</html>
