(function(){const e=["events","filteredEvents","showAddEventModal","newEvent","adverts","filteredAdverts","showAddAdvertModal","newAdvert","bibleVerse","showBibleModal","bibleVerses","searchQuery","isLoading","breakReminder","showBreakReminder","churchEvents","churchAdverts"];e.forEach(e=>{if(typeof window[e]==="undefined"){if(e.includes("show")||e.includes("isLoading")){window[e]=false}else if(e.includes("Events")||e.includes("Adverts")||e.includes("Verses")){window[e]=[]}else if(e.includes("new")){window[e]={title:"",description:"",date:"",link:"",linkText:"Learn More",expiryDate:""}}else if(e==="searchQuery"){window[e]=""}else{window[e]={}}console.log(`Initialized missing variable: ${e}`)}});document.addEventListener("DOMContentLoaded",function(){if(typeof window.Alpine==="undefined"){console.warn("Alpine.js not loaded. Attempting to load it...");const e=document.createElement("script");e.src="https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js";e.defer=true;document.head.appendChild(e)}else{console.log("Alpine.js is loaded.")}})})();document.addEventListener('alpine:init', () => {
    Alpine.data('blogPage', () => ({
        showToast: false,
        toastMessage: '',
        toastType: 'success',
        toastIcon: 'fa-check',
        loading: false,
        breakReminder: {
            show: false,
            message: '',
            type: 'success',
            icon: 'fa-check',
        },
        
        // Church Events
        events: [],
        filteredEvents: [],
        showAddEventModal: false,
        newEvent: {
            title: '',
            date: '',
            description: '',
            link: '',
            linkText: 'Learn More',
            expiryDate: ''
        },
        
        // Church Adverts
        adverts: [],
        filteredAdverts: [],
        showAddAdvertModal: false,
        newAdvert: {
            title: '',
            description: '',
            link: '',
            linkText: 'View Details',
            expiryDate: ''
        },
        
        // Bible Verse
        bibleVerse: null,
        showBibleModal: false,
        bibleVerses: [],
        searchQuery: '',
        isLoading: false,

        // Toast Methods
        showSuccessToast(message) {
            this.toastMessage = message;
            this.toastType = 'success';
            this.toastIcon = 'fa-check';
            this.showToast = true;
            setTimeout(() => this.showToast = false, 3000);
        },

        showErrorToast(message) {
            this.toastMessage = message;
            this.toastType = 'error';
            this.toastIcon = 'fa-exclamation-circle';
            this.showToast = true;
            setTimeout(() => this.showToast = false, 3000);
        },

        // Church Events Methods
        async loadChurchEvents() {
            try {
                const response = await fetch('/api/events');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                this.events = data.events || [];
                this.filterEvents();
            } catch (error) {
                console.error('Error loading church events:', error);
                this.showErrorToast('Failed to load church events');
                this.events = [];
                this.filteredEvents = [];
            }
        },

        filterEvents() {
            if (!Array.isArray(this.events)) {
                this.filteredEvents = [];
                return;
            }
            const now = new Date();
            this.filteredEvents = this.events.filter(event => {
                const eventDate = new Date(event.date);
                return eventDate >= now;
            });
        },

        // Church Adverts Methods
        async loadChurchAdverts() {
            try {
                const response = await fetch('/api/adverts');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                this.adverts = data.adverts || [];
                this.filterAdverts();
            } catch (error) {
                console.error('Error loading church adverts:', error);
                this.showErrorToast('Failed to load church adverts');
                this.adverts = [];
                this.filteredAdverts = [];
            }
        },

        filterAdverts() {
            if (!Array.isArray(this.adverts)) {
                this.filteredAdverts = [];
                return;
            }
            const now = new Date();
            this.filteredAdverts = this.adverts.filter(advert => {
                const expiryDate = new Date(advert.expiryDate);
                return expiryDate >= now;
            });
        },

        // Bible Verse Methods
        async loadBibleVerse() {
            try {
                const response = await fetch('/api/daily-verse');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                this.bibleVerse = data;
            } catch (error) {
                console.error('Error loading daily verse:', error);
                this.showErrorToast('Failed to load daily verse');
                this.bibleVerse = null;
            }
        }
    }));
});
