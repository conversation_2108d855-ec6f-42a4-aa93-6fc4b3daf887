/**
 * SimpleWebAuthn Browser Polyfill
 * 
 * This is a simplified version of the SimpleWebAuthn browser library
 * that provides basic functionality for WebAuthn operations.
 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.SimpleWebAuthn = {}));
})(this, (function (exports) { 'use strict';

  // Check if WebAuthn is supported
  const browserSupportsWebAuthn = () => {
    return typeof window !== 'undefined' && 
           typeof window.PublicKeyCredential !== 'undefined';
  };

  // Check if browser supports WebAuthn autofill
  const browserSupportsWebAuthnAutofill = async () => {
    if (!browserSupportsWebAuthn()) {
      return false;
    }

    try {
      return await PublicKeyCredential.isConditionalMediationAvailable();
    } catch (error) {
      return false;
    }
  };

  // Check if user verifying platform authenticator is available
  const platformAuthenticatorIsAvailable = async () => {
    if (!browserSupportsWebAuthn()) {
      return false;
    }

    try {
      return await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
    } catch (error) {
      return false;
    }
  };

  // Start registration
  const startRegistration = async (options) => {
    if (!browserSupportsWebAuthn()) {
      throw new Error('WebAuthn is not supported in this browser');
    }

    // Convert base64url strings to ArrayBuffers
    const publicKey = {
      ...options,
      challenge: base64URLStringToBuffer(options.challenge),
      user: {
        ...options.user,
        id: base64URLStringToBuffer(options.user.id),
      },
      excludeCredentials: options.excludeCredentials?.map(credential => ({
        ...credential,
        id: base64URLStringToBuffer(credential.id),
      })),
    };

    // Create credential
    let credential;
    try {
      credential = await navigator.credentials.create({ publicKey });
    } catch (error) {
      throw error;
    }

    // Convert ArrayBuffers to base64url strings
    return {
      id: credential.id,
      rawId: bufferToBase64URLString(credential.rawId),
      response: {
        clientDataJSON: bufferToBase64URLString(credential.response.clientDataJSON),
        attestationObject: bufferToBase64URLString(credential.response.attestationObject),
      },
      type: credential.type,
      clientExtensionResults: credential.getClientExtensionResults(),
    };
  };

  // Start authentication
  const startAuthentication = async (options) => {
    if (!browserSupportsWebAuthn()) {
      throw new Error('WebAuthn is not supported in this browser');
    }

    // Convert base64url strings to ArrayBuffers
    const publicKey = {
      ...options,
      challenge: base64URLStringToBuffer(options.challenge),
      allowCredentials: options.allowCredentials?.map(credential => ({
        ...credential,
        id: base64URLStringToBuffer(credential.id),
      })),
    };

    // Get credential
    let credential;
    try {
      credential = await navigator.credentials.get({ publicKey });
    } catch (error) {
      throw error;
    }

    // Convert ArrayBuffers to base64url strings
    const response = {
      id: credential.id,
      rawId: bufferToBase64URLString(credential.rawId),
      response: {
        clientDataJSON: bufferToBase64URLString(credential.response.clientDataJSON),
        authenticatorData: bufferToBase64URLString(credential.response.authenticatorData),
        signature: bufferToBase64URLString(credential.response.signature),
      },
      type: credential.type,
      clientExtensionResults: credential.getClientExtensionResults(),
    };

    // Add userHandle if it exists
    if (credential.response.userHandle) {
      response.response.userHandle = bufferToBase64URLString(credential.response.userHandle);
    }

    return response;
  };

  // Helper function to convert base64url string to ArrayBuffer
  const base64URLStringToBuffer = (base64URLString) => {
    // Convert from base64url to base64
    const base64 = base64URLString.replace(/-/g, '+').replace(/_/g, '/');
    // Add padding
    const padding = '='.repeat((4 - (base64.length % 4)) % 4);
    const base64Padded = base64 + padding;
    // Convert to binary string
    const binary = atob(base64Padded);
    // Convert to ArrayBuffer
    const buffer = new ArrayBuffer(binary.length);
    const view = new Uint8Array(buffer);
    for (let i = 0; i < binary.length; i++) {
      view[i] = binary.charCodeAt(i);
    }
    return buffer;
  };

  // Helper function to convert ArrayBuffer to base64url string
  const bufferToBase64URLString = (buffer) => {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64 = btoa(binary);
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  };

  // Export functions
  exports.browserSupportsWebAuthn = browserSupportsWebAuthn;
  exports.browserSupportsWebAuthnAutofill = browserSupportsWebAuthnAutofill;
  exports.platformAuthenticatorIsAvailable = platformAuthenticatorIsAvailable;
  exports.startRegistration = startRegistration;
  exports.startAuthentication = startAuthentication;

}));
