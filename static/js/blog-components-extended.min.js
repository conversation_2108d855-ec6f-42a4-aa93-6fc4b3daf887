document.addEventListener("DOMContentLoaded",function(){if("undefined"!=typeof window.Alpine){(function(){const e=window.Alpine.data("churchEvents");if(e){try{Object.assign(e().prototype,{get filteredEvents(){const e=(new Date).toISOString().split("T")[0];return this.events.filter(t=>t.expiryDate>=e)},formatDate(e){return new Date(e).toLocaleDateString(void 0,{weekday:"long",year:"numeric",month:"long",day:"numeric"})},addEvent(){const e=Date.now();this.events.push({id:e,title:this.newEvent.title,date:this.newEvent.date,description:this.newEvent.description,link:this.newEvent.link||"#",linkText:this.newEvent.linkText,expiryDate:this.newEvent.expiryDate}),this.saveEvents(),this.resetEventForm(),this.showAddEventModal=!1},removeEvent(e){this.events=this.events.filter(t=>t.id!==e),this.saveEvents()},removeExpiredEvents(){const e=(new Date).toISOString().split("T")[0],t=this.events.filter(t=>t.expiryDate<e);t.length>0&&(console.log(`Removed ${t.length} expired events`),this.events=this.events.filter(t=>t.expiryDate>=e),this.saveEvents())},saveEvents(){localStorage.setItem("churchEvents",JSON.stringify(this.events))},resetEventForm(){this.newEvent={title:"",date:"",description:"",link:"",linkText:"Register for Event",expiryDate:""}}});}catch(err){console.error("Error extending churchEvents component:", err);}}})();(function(){const e=window.Alpine.data("churchAdverts");if(e){try{Object.assign(e().prototype,{get filteredAdverts(){const e=(new Date).toISOString().split("T")[0];return this.adverts.filter(t=>t.expiryDate>=e)},addAdvert(){const e=Date.now();this.adverts.push({id:e,title:this.newAdvert.title,description:this.newAdvert.description,link:this.newAdvert.link||"#",linkText:this.newAdvert.linkText,expiryDate:this.newAdvert.expiryDate}),this.saveAdverts(),this.resetAdvertForm(),this.showAddAdvertModal=!1},removeAdvert(e){this.adverts=this.adverts.filter(t=>t.id!==e),this.saveAdverts()},removeExpiredAdverts(){const e=(new Date).toISOString().split("T")[0],t=this.adverts.filter(t=>t.expiryDate<e);t.length>0&&(console.log(`Removed ${t.length} expired announcements`),this.adverts=this.adverts.filter(t=>t.expiryDate>=e),this.saveAdverts())},saveAdverts(){localStorage.setItem("churchAdverts",JSON.stringify(this.adverts))},resetAdvertForm(){this.newAdvert={title:"",description:"",link:"",linkText:"View Announcement Details",expiryDate:""}}});}catch(err){console.error("Error extending churchAdverts component:", err);}}})()} else {console.warn("Alpine.js not available, extended components not loaded");}});
