/**
 * Break Reminder Component
 * 
 * This script initializes the break reminder functionality for the DavarTruth application.
 * It reminds users to take breaks after extended periods of reading or writing.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Break reminder script loaded');
    
    // Check if Alpine.js is available
    if (typeof window.Alpine === 'undefined') {
        console.error('Alpine.js not loaded. Break reminder will not function properly.');
        
        // Try to load Alpine.js
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js';
        script.defer = true;
        script.onload = function() {
            console.log('Alpine.js loaded from break-reminder.js');
            initBreakReminder();
        };
        document.head.appendChild(script);
    } else {
        // Initialize the break reminder component
        initBreakReminder();
    }
    
    function initBreakReminder() {
        if (!window.Alpine.data('breakReminder')) {
            window.Alpine.data('breakReminder', function() {
                return {
                    showBreakReminder: false,
                    breakReminderInterval: 60 * 60 * 1000, // 1 hour in milliseconds
                    breakReminderTimer: null,
                    lastBreakTime: null,
                    
                    init() {
                        console.log('Break reminder component initialized');
                        this.initBreakReminder();
                    },
                    
                    initBreakReminder() {
                        // Check if we have a stored last break time
                        const storedLastBreakTime = localStorage.getItem('lastBreakTime');
                        if (storedLastBreakTime) {
                            this.lastBreakTime = parseInt(storedLastBreakTime);
                            const timeElapsed = Date.now() - this.lastBreakTime;
                            
                            // If it's been less than the interval since the last break, set timer for remaining time
                            if (timeElapsed < this.breakReminderInterval) {
                                const remainingTime = this.breakReminderInterval - timeElapsed;
                                this.breakReminderTimer = setTimeout(function() {
                                    this.showBreakReminder = true;
                                }.bind(this), remainingTime);
                            } else {
                                // If it's been longer than the interval, show reminder immediately
                                this.showBreakReminder = true;
                            }
                        } else {
                            // If no stored time, set timer for full interval
                            this.breakReminderTimer = setTimeout(function() {
                                this.showBreakReminder = true;
                            }.bind(this), this.breakReminderInterval);
                        }
                    },
                    
                    takeBreak() {
                        this.showBreakReminder = false;
                        this.lastBreakTime = Date.now();
                        localStorage.setItem('lastBreakTime', this.lastBreakTime);
                        
                        // Reset the timer
                        clearTimeout(this.breakReminderTimer);
                        this.breakReminderTimer = setTimeout(function() {
                            this.showBreakReminder = true;
                        }.bind(this), this.breakReminderInterval);
                    },
                    
                    continueReading() {
                        this.showBreakReminder = false;
                        
                        // Set a shorter reminder interval (15 minutes)
                        clearTimeout(this.breakReminderTimer);
                        this.breakReminderTimer = setTimeout(function() {
                            this.showBreakReminder = true;
                        }.bind(this), 15 * 60 * 1000);
                    }
                };
            });
            
            console.log('Break reminder component registered');
        } else {
            console.log('Break reminder component already registered');
        }
    }
});
