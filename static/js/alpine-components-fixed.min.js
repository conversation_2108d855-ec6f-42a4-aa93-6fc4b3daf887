window.events=[];window.filteredEvents=[];window.showAddEventModal=false;window.newEvent={title:"",date:"",description:"",link:"",linkText:"Learn More",expiryDate:""};window.adverts=[];window.filteredAdverts=[];window.showAddAdvertModal=false;window.newAdvert={title:"",description:"",link:"",linkText:"View Announcement Details",expiryDate:""};window.bibleVerse={};window.showBibleModal=false;window.bibleVerses=[];window.searchQuery="";window.isLoading=false;window.breakReminder={};window.showBreakReminder=false;document.addEventListener("DOMContentLoaded",function(){console.log("Alpine components consolidated initialization loaded");function e(){if(typeof window.Alpine==="undefined"){console.error("Alpine.js still not available after loading attempt");return}try{window.Alpine.data("churchEvents",function(){return{events:[],filteredEvents:[],showAddEventModal:false,newEvent:{title:"",date:"",description:"",link:"",linkText:"Learn More",expiryDate:""},init(){console.log("Church events component initialized");const e=localStorage.getItem("churchEvents");this.events=e?JSON.parse(e):[];this.removeExpiredEvents();this.updateFilteredEvents()},updateFilteredEvents(){const e=(new Date).toISOString().split("T")[0];this.filteredEvents=this.events.filter(t=>t.expiryDate>=e)},removeExpiredEvents(){const e=(new Date).toISOString().split("T")[0];const t=this.events.filter(t=>t.expiryDate<e);if(t.length>0){console.log(`Removed ${t.length} expired events`);this.events=this.events.filter(t=>t.expiryDate>=e);this.saveEvents()}},saveEvents(){localStorage.setItem("churchEvents",JSON.stringify(this.events))},resetEventForm(){this.newEvent={title:"",date:"",description:"",link:"",linkText:"Learn More",expiryDate:""}}}});window.Alpine.data("churchAdverts",function(){return{adverts:[],filteredAdverts:[],showAddAdvertModal:false,newAdvert:{title:"",description:"",link:"",linkText:"View Announcement Details",expiryDate:""},init(){console.log("Church adverts component initialized");const e=localStorage.getItem("churchAdverts");this.adverts=e?JSON.parse(e):[];this.removeExpiredAdverts();this.updateFilteredAdverts()},updateFilteredAdverts(){const e=(new Date).toISOString().split("T")[0];this.filteredAdverts=this.adverts.filter(t=>t.expiryDate>=e)},removeExpiredAdverts(){const e=(new Date).toISOString().split("T")[0];const t=this.adverts.filter(t=>t.expiryDate<e);if(t.length>0){console.log(`Removed ${t.length} expired announcements`);this.adverts=this.adverts.filter(t=>t.expiryDate>=e);this.saveAdverts()}},saveAdverts(){localStorage.setItem("churchAdverts",JSON.stringify(this.adverts))},resetAdvertForm(){this.newAdvert={title:"",description:"",link:"",linkText:"View Announcement Details",expiryDate:""}}}});window.Alpine.data("bibleVerse",function(){return{showBibleModal:false,currentPostId:null,bibleVerses:[],searchQuery:"",isLoading:false,init(){console.log("Bible verse component initialized")},searchBibleVerses(){if(!this.searchQuery||!this.searchQuery.trim())return;this.isLoading=true;this.bibleVerses=[];fetch(`/api/bible/search?q=${encodeURIComponent(this.searchQuery)}`).then(e=>e.json()).then(e=>{this.bibleVerses=e.verses||[];this.isLoading=false}).catch(e=>{console.error("Error searching Bible verses:",e);this.isLoading=false})}}});window.Alpine.data("breakReminder",function(){return{showBreakReminder:false,breakReminderInterval:36e5,breakReminderTimer:null,lastBreakTime:null,init(){console.log("Break reminder component initialized");this.initBreakReminder()},initBreakReminder(){const e=localStorage.getItem("lastBreakTime");if(e){this.lastBreakTime=parseInt(e);const t=Date.now()-this.lastBreakTime;if(t<this.breakReminderInterval){this.breakReminderTimer=setTimeout(()=>{this.showBreakReminder=true},this.breakReminderInterval-t)}else{this.showBreakReminder=true}}else{this.breakReminderTimer=setTimeout(()=>{this.showBreakReminder=true},this.breakReminderInterval)}},takeBreak(){this.showBreakReminder=false;this.lastBreakTime=Date.now();localStorage.setItem("lastBreakTime",this.lastBreakTime);clearTimeout(this.breakReminderTimer);this.breakReminderTimer=setTimeout(()=>{this.showBreakReminder=true},this.breakReminderInterval)},continueReading(){this.showBreakReminder=false;clearTimeout(this.breakReminderTimer);this.breakReminderTimer=setTimeout(()=>{this.showBreakReminder=true},9e5)}}});console.log("All Alpine.js components initialized successfully")}catch(e){console.error("Error initializing Alpine.js components:",e)}}if(typeof window.Alpine==="undefined"){console.error("Alpine.js not loaded. Loading it now...");const t=document.createElement("script");t.src="https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js";t.defer=true;t.onload=function(){console.log("Alpine.js loaded dynamically");e()};document.head.appendChild(t)}else{console.log("Alpine.js already loaded, initializing components");e()}});
