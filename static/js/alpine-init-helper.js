/**
 * Alpine.js Initialization Helper
 * 
 * This script ensures that all Alpine.js components are properly initialized
 * and all required variables are defined to prevent reference errors.
 */

// Define global variables to prevent reference errors
(function() {
    // Create a list of all variables that might be referenced
    const variables = [
        'events', 'filteredEvents', 'showAddEventModal', 'newEvent',
        'adverts', 'filteredAdverts', 'showAddAdvertModal', 'newAdvert',
        'bibleVerse', 'showBibleModal', 'bibleVerses', 'searchQuery', 'isLoading',
        'breakReminder', 'showBreakReminder', 'churchEvents', 'churchAdverts'
    ];
    
    // Define each variable on the window object if it doesn't already exist
    variables.forEach(variable => {
        if (typeof window[variable] === 'undefined') {
            // Set default values based on variable type
            if (variable.includes('show') || variable.includes('isLoading')) {
                window[variable] = false;
            } else if (variable.includes('Events') || variable.includes('Adverts') || variable.includes('Verses')) {
                window[variable] = [];
            } else if (variable.includes('new')) {
                window[variable] = {
                    title: '',
                    description: '',
                    date: '',
                    link: '',
                    linkText: 'Learn More',
                    expiryDate: ''
                };
            } else if (variable === 'searchQuery') {
                window[variable] = '';
            } else {
                window[variable] = {};
            }
            
            console.log(`Initialized missing variable: ${variable}`);
        }
    });
    
    // Check if Alpine.js is loaded
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof window.Alpine === 'undefined') {
            console.warn('Alpine.js not loaded. Attempting to load it...');
            
            // Create a script element to load Alpine.js
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js';
            script.defer = true;
            
            // Add the script to the document
            document.head.appendChild(script);
        } else {
            console.log('Alpine.js is loaded.');
        }
    });
})();
