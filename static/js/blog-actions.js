/**
 * Blog post actions functionality
 * Handles comment, bookmark, and Bible verse features
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize bookmark buttons
    initializeBookmarkButtons();

    // Initialize comment functionality
    initializeCommentButtons();

    // Initialize Bible verse functionality
    initializeBibleVerseButtons();

    // Initialize report abuse functionality
    initializeReportButtons();
});

/**
 * Initialize bookmark functionality
 */
function initializeBookmarkButtons() {
    const bookmarkButtons = document.querySelectorAll('.bookmark-button');

    bookmarkButtons.forEach(button => {
        const postId = button.getAttribute('data-post-id');

        // Check if post is already bookmarked
        const isBookmarked = isPostBookmarked(postId);
        updateBookmarkButtonUI(button, isBookmarked);

        // Add click event listener
        button.addEventListener('click', function() {
            toggleBookmark(postId, button);
        });
    });
}

/**
 * Check if a post is already bookmarked
 */
function isPostBookmarked(postId) {
    const bookmarks = getBookmarks();
    return bookmarks.includes(postId);
}

/**
 * Get bookmarks from localStorage
 */
function getBookmarks() {
    const bookmarksJSON = localStorage.getItem('davartruth_bookmarks');
    return bookmarksJSON ? JSON.parse(bookmarksJSON) : [];
}

/**
 * Save bookmarks to localStorage
 */
function saveBookmarks(bookmarks) {
    localStorage.setItem('davartruth_bookmarks', JSON.stringify(bookmarks));
}

/**
 * Toggle bookmark status for a post
 */
function toggleBookmark(postId, button) {
    const bookmarks = getBookmarks();
    const isBookmarked = bookmarks.includes(postId);

    if (isBookmarked) {
        // Remove bookmark
        const index = bookmarks.indexOf(postId);
        if (index > -1) {
            bookmarks.splice(index, 1);
        }
        showToast('Post removed from bookmarks');
    } else {
        // Add bookmark
        bookmarks.push(postId);
        showToast('Post added to bookmarks');
    }

    // Save updated bookmarks
    saveBookmarks(bookmarks);

    // Update button UI
    updateBookmarkButtonUI(button, !isBookmarked);
}

/**
 * Update bookmark button UI based on bookmark status
 */
function updateBookmarkButtonUI(button, isBookmarked) {
    const icon = button.querySelector('i');

    if (isBookmarked) {
        // Bookmarked state
        icon.classList.remove('far');
        icon.classList.add('fas');
        button.setAttribute('title', 'Remove bookmark');
    } else {
        // Not bookmarked state
        icon.classList.remove('fas');
        icon.classList.add('far');
        button.setAttribute('title', 'Bookmark this post');
    }
}

/**
 * Initialize comment functionality
 */
function initializeCommentButtons() {
    const commentButtons = document.querySelectorAll('.comment-button');

    commentButtons.forEach(button => {
        button.addEventListener('click', function() {
            const postId = button.getAttribute('data-post-id');
            console.log('Comment button clicked for post ID:', postId);
            toggleComments(postId);
        });
    });
}

/**
 * Toggle comments section for a post
 */
async function toggleComments(postId) {
    // Check if comments section already exists
    let commentsSection = document.querySelector(`#comments-section-${postId}`);

    if (commentsSection) {
        // Toggle visibility if it exists
        if (commentsSection.classList.contains('hidden')) {
            commentsSection.classList.remove('hidden');
        } else {
            commentsSection.classList.add('hidden');
        }
    } else {
        // Create comments section if it doesn't exist
        const postElement = document.querySelector(`article[data-post-id="${postId}"]`);
        if (!postElement) return;

        // Create comments section
        commentsSection = document.createElement('div');
        commentsSection.id = `comments-section-${postId}`;
        commentsSection.className = 'mt-4 pt-4 border-t border-gray-100';

        // Check if we already have a comment form for this post
        const existingForm = document.querySelector(`#comment-form-${postId}`);
        if (!existingForm) {
            // Add comment form - await since it's now async
            const commentForm = await createCommentForm(postId);
            commentsSection.appendChild(commentForm);
        }

        // Add to the post
        const footer = postElement.querySelector('footer');
        footer.parentNode.insertBefore(commentsSection, footer.nextSibling);
    }
}

/**
 * Create comment form
 */
async function createCommentForm(postId) {
    const formContainer = document.createElement('div');
    formContainer.className = 'mb-4';

    // Check if user is logged in
    const isLoggedIn = document.body.classList.contains('user-logged-in');

    // Get comment count - await the Promise
    let commentCount = 0;
    try {
        commentCount = await getCommentCount(postId);
    } catch (error) {
        console.error('Error getting comment count:', error);
    }

    if (isLoggedIn) {
        // Create form for logged in users
        formContainer.innerHTML = `
            <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-semibold text-primary-custom">Comments (${commentCount})</h3>
            </div>
            <form id="comment-form-${postId}" class="comment-form">
                <textarea
                    name="comment"
                    placeholder="Share your thoughts..."
                    class="w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-custom"
                    rows="3"
                ></textarea>
                <div class="flex justify-between items-center mt-2">
                    <div class="flex items-center">
                        <div class="emoji-picker-trigger mr-2">
                            <button type="button" class="text-gray-500 hover:text-primary-custom focus:outline-none" onclick="toggleEmojiPicker('${postId}')">
                                <i class="far fa-smile text-xl"></i>
                            </button>
                        </div>
                        <div class="text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Comments are subject to community guidelines
                        </div>
                    </div>
                    <button type="submit" class="bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded">
                        <i class="far fa-paper-plane mr-1"></i> Submit
                    </button>
                </div>
                <div id="emoji-picker-${postId}" class="emoji-picker hidden mt-2 p-2 bg-white border rounded-lg shadow-lg">
                    <div class="grid grid-cols-8 gap-1">
                        ${generateReligiousEmojis()}
                    </div>
                </div>
            </form>
        `;

        // Add submit event listener
        setTimeout(() => {
            const form = document.getElementById(`comment-form-${postId}`);
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    submitComment(postId, form);
                });
            }
        }, 0);
    } else {
        // Create login prompt for non-logged in users
        formContainer.innerHTML = `
            <div class="bg-gray-50 p-4 rounded-lg text-center">
                <p class="mb-2">Please log in to leave a comment</p>
                <a href="/login?next=/blog" class="inline-block bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded">
                    <i class="fas fa-sign-in-alt mr-1"></i> Log In
                </a>
            </div>
        `;
    }

    return formContainer;
}

/**
 * Submit a comment
 */
function submitComment(postId, form) {
    const textarea = form.querySelector('textarea');
    const comment = textarea.value.trim();

    if (!comment) {
        showToast('Please enter a comment', 'error');
        return;
    }

    // Send the comment to the server
    fetch(`/api/posts/${postId}/comments`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content: comment
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Thank you for your comment!', 'success');

            // Update comment count
            updateCommentCount(postId);

            // Add the comment to the UI
            addCommentToUI(postId, comment);
            textarea.value = '';
        } else {
            showToast(data.error || 'Failed to post comment', 'error');
        }
    })
    .catch(error => {
        console.error('Error posting comment:', error);
        showToast('An error occurred while posting your comment', 'error');
    });

    // Hide emoji picker if it's open
    const emojiPicker = document.getElementById(`emoji-picker-${postId}`);
    if (emojiPicker && !emojiPicker.classList.contains('hidden')) {
        emojiPicker.classList.add('hidden');
    }
}

/**
 * Add a comment to the UI
 */
function addCommentToUI(postId, commentText, parentId = null) {
    // Find the comments section
    const commentsSection = document.querySelector(`#comments-section-${postId}`);
    if (!commentsSection) return;

    // Generate a unique ID for this comment to prevent duplicates
    const commentId = `comment-${postId}-${Date.now()}`;

    // Check if this comment already exists (prevent duplicates)
    if (document.getElementById(commentId)) {
        console.log('Comment already exists, not adding duplicate');
        return;
    }

    // Check if there's already a comments list
    let commentsList = commentsSection.querySelector('.comments-list');

    // If not, create one
    if (!commentsList) {
        commentsList = document.createElement('div');
        commentsList.className = 'comments-list mt-4';
        commentsSection.appendChild(commentsList);
    }

    // Create a new comment element
    const commentElement = document.createElement('div');
    commentElement.id = commentId; // Set the unique ID
    commentElement.className = 'comment bg-gray-50 p-3 rounded-lg mb-3';
    commentElement.dataset.commentId = commentId.split('-').pop(); // Store the numeric ID for API calls

    // Get current user info (in a real app, this would come from the server)
    const isLoggedIn = document.body.classList.contains('user-logged-in');
    const username = isLoggedIn ? 'You' : 'Guest';

    // Format the date
    const now = new Date();
    const formattedDate = now.toLocaleDateString() + ' ' + now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    // Add the comment content
    commentElement.innerHTML = `
        <div class="flex items-start">
            <div class="w-8 h-8 rounded-full bg-primary-custom text-white flex items-center justify-center mr-2">
                <i class="fas fa-user"></i>
            </div>
            <div class="flex-grow">
                <div class="flex justify-between items-center mb-1">
                    <span class="font-medium">${username}</span>
                    <span class="text-xs text-gray-500">${formattedDate}</span>
                </div>
                <p class="text-sm">${commentText.replace(/\n/g, '<br>')}</p>
                <div class="mt-2">
                    <button class="text-xs text-primary-custom hover:underline reply-button" data-comment-id="${commentId.split('-').pop()}">
                        <i class="fas fa-reply mr-1"></i> Reply
                    </button>
                </div>
                <div class="reply-form-container mt-2 hidden"></div>
                <div class="replies-container mt-2"></div>
            </div>
        </div>
    `;

    // If this is a reply, add it to the parent comment's replies container
    if (parentId) {
        const parentComment = document.querySelector(`[data-comment-id="${parentId}"]`);
        if (parentComment) {
            const repliesContainer = parentComment.querySelector('.replies-container');
            if (repliesContainer) {
                // Add a left border to indicate nesting
                commentElement.classList.add('ml-8', 'border-l-2', 'border-gray-200', 'pl-3');
                repliesContainer.appendChild(commentElement);
                return;
            }
        }
    }

    // If not a reply or parent not found, add to the main comments list
    commentsList.appendChild(commentElement);

    // Add event listener for reply button
    const replyButton = commentElement.querySelector('.reply-button');
    if (replyButton) {
        replyButton.addEventListener('click', function() {
            showReplyForm(postId, this.dataset.commentId);
        });
    }
}

/**
 * Show reply form for a comment
 */
function showReplyForm(postId, commentId) {
    // Find the comment element
    const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
    if (!commentElement) return;

    // Find the reply form container
    const replyFormContainer = commentElement.querySelector('.reply-form-container');
    if (!replyFormContainer) return;

    // Toggle visibility
    if (replyFormContainer.classList.contains('hidden')) {
        // Create the reply form if it doesn't exist
        if (replyFormContainer.children.length === 0) {
            replyFormContainer.innerHTML = `
                <form class="reply-form mt-2">
                    <textarea
                        name="reply"
                        placeholder="Write a reply..."
                        class="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-custom"
                        rows="2"
                    ></textarea>
                    <div class="flex justify-end mt-2">
                        <button type="button" class="cancel-reply-button mr-2 px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm">
                            Cancel
                        </button>
                        <button type="submit" class="submit-reply-button px-3 py-1 bg-primary-custom hover:bg-primary-dark text-white rounded text-sm">
                            Reply
                        </button>
                    </div>
                </form>
            `;

            // Add event listeners
            const form = replyFormContainer.querySelector('form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const textarea = this.querySelector('textarea');
                submitComment(postId, form, commentId);
            });

            const cancelButton = form.querySelector('.cancel-reply-button');
            cancelButton.addEventListener('click', function() {
                replyFormContainer.classList.add('hidden');
            });
        }

        replyFormContainer.classList.remove('hidden');
        // Focus the textarea
        const textarea = replyFormContainer.querySelector('textarea');
        if (textarea) textarea.focus();
    } else {
        replyFormContainer.classList.add('hidden');
    }
}

/**
 * Initialize Bible verse buttons
 */
function initializeBibleVerseButtons() {
    // The Bible verse functionality is already implemented in the Alpine.js component
    // We just need to make sure the buttons work correctly

    const bibleButtons = document.querySelectorAll('.bible-button');

    bibleButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Open the Bible search modal
            const bibleModal = document.getElementById('bible-search-modal');
            if (bibleModal) {
                bibleModal.classList.remove('hidden');
            }
        });
    });
}

/**
 * Initialize report abuse functionality
 */
function initializeReportButtons() {
    // This function is now handled by direct onclick handlers
    console.log('Report abuse functionality initialized');
}

/**
 * Open the report modal
 */
function openReportModal(postId) {
    console.log('Opening report modal for post ID:', postId);
    const reportModal = document.getElementById('report-abuse-modal');

    if (reportModal) {
        // Make the modal visible by removing the hidden class
        reportModal.classList.remove('hidden');
        reportModal.style.display = 'flex';

        // Store the post ID in a data attribute
        reportModal.setAttribute('data-post-id', postId);
    } else {
        console.error('Report modal not found');
    }
}

/**
 * Close the report modal
 */
function closeReportModal() {
    console.log('Closing report modal');
    const reportModal = document.getElementById('report-abuse-modal');

    if (reportModal) {
        // Hide the modal
        reportModal.classList.add('hidden');
        reportModal.style.display = 'none';
    }

}

/**
 * Submit a report directly
 */
function submitReportDirectly() {
    console.log('Submitting report directly');
    const reportModal = document.getElementById('report-abuse-modal');

    if (!reportModal) {
        console.error('Report modal not found');
        return;
    }

    // Get form values
    const reasonSelect = document.getElementById('report-reason');
    const detailsTextarea = document.getElementById('report-details');

    if (!reasonSelect || !detailsTextarea) {
        console.error('Form elements not found');
        return;
    }

    const reason = reasonSelect.value;
    const details = detailsTextarea.value.trim();
    const postId = reportModal.getAttribute('data-post-id');

    // Validate input
    if (!reason || !details) {
        showToast('Please fill out all fields', 'error');
        return;
    }

    // Send the report to the server
    fetch('/api/report-content', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content_type: 'blog_post',
            content_id: postId,
            reason: reason,
            details: details
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message || 'Thank you for your report. Our team will review it shortly.', 'success');

            // Reset form and close modal
            reasonSelect.value = '';
            detailsTextarea.value = '';
            closeReportModal();
        } else {
            showToast(data.error || 'Failed to submit report', 'error');
        }
    })
    .catch(error => {
        console.error('Error submitting report:', error);
        showToast('An error occurred while submitting your report', 'error');
    });
}

/**
 * Submit a report (Alpine.js method)
 */
function submitReport() {
    const postId = this.currentPostId;
    const reason = this.reportReason;
    const details = this.reportDetails;

    if (!reason || !details) {
        showToast('Please fill out all fields', 'error');
        return;
    }

    // Send the report to the server
    fetch('/api/report-content', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content_type: 'blog_post',
            content_id: postId,
            reason: reason,
            details: details
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message || 'Thank you for your report. Our team will review it shortly.', 'success');

            // Reset form and close modal
            this.reportReason = '';
            this.reportDetails = '';
            this.showReportModal = false;
        } else {
            showToast(data.error || 'Failed to submit report', 'error');
        }
    })
    .catch(error => {
        console.error('Error submitting report:', error);
        showToast('An error occurred while submitting your report', 'error');
    });
}

/**
 * Get comment count for a post
 */
function getCommentCount(postId) {
    // Fetch the count from the server
    return fetch(`/api/posts/${postId}/comments`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                return data.comments.length;
            }
            return 0;
        })
        .catch(error => {
            console.error('Error fetching comments:', error);
            return 0;
        });
}

/**
 * Update comment count for a post
 */
function updateCommentCount(postId) {
    // Fetch the updated count from the server
    getCommentCount(postId)
        .then(count => {
            // Update the UI if the comment count element exists
            const commentCountElement = document.querySelector(`#comments-section-${postId} h3`);
            if (commentCountElement) {
                commentCountElement.textContent = `Comments (${count})`;
            }

            // Also update the comment button count
            const commentButton = document.getElementById(`comment-button-${postId}`);
            if (commentButton) {
                const countSpan = commentButton.querySelector('.comment-count');
                if (countSpan) {
                    countSpan.textContent = count > 0 ? `(${count})` : '';
                }
            }

            return count;
        });
}

/**
 * Generate religious emojis for the picker
 */
function generateReligiousEmojis() {
    // Array of religious and positive emojis with their HTML entities or Unicode
    const emojis = [
        { symbol: '✝️', name: 'Cross' },
        { symbol: '📖', name: 'Bible' },
        { symbol: '🙏', name: 'Praying Hands' },
        { symbol: '❤️', name: 'Heart' },
        { symbol: '✨', name: 'Sparkles' },
        { symbol: '🕊️', name: 'Dove' },
        { symbol: '🔥', name: 'Fire' },
        { symbol: '👼', name: 'Angel' },
        { symbol: '🌟', name: 'Star' },
        { symbol: '🌞', name: 'Sun' },
        { symbol: '🌱', name: 'Seedling' },
        { symbol: '🍇', name: 'Grapes' },
        { symbol: '🍞', name: 'Bread' },
        { symbol: '🐟', name: 'Fish' },
        { symbol: '🐑', name: 'Sheep' },
        { symbol: '🕯️', name: 'Candle' },
        { symbol: '🌿', name: 'Herb' },
        { symbol: '🌊', name: 'Wave' },
        { symbol: '😇', name: 'Halo' },
        { symbol: '🤲', name: 'Palms Up' },
        { symbol: '👏', name: 'Clapping' },
        { symbol: '🙌', name: 'Raised Hands' }
    ];

    // Generate HTML for each emoji button
    return emojis.map(emoji => `
        <button type="button" class="emoji-btn p-2 hover:bg-gray-100 rounded" title="${emoji.name}" onclick="insertEmoji('${emoji.symbol}')">
            <span class="text-xl">${emoji.symbol}</span>
        </button>
    `).join('');
}

/**
 * Toggle emoji picker visibility
 */
function toggleEmojiPicker(postId) {
    const emojiPicker = document.getElementById(`emoji-picker-${postId}`);
    if (emojiPicker) {
        emojiPicker.classList.toggle('hidden');
    }
}

/**
 * Insert emoji into comment textarea
 */
function insertEmoji(emoji) {
    // Find the active textarea (the one that was last focused)
    const activeTextarea = document.activeElement;
    if (activeTextarea && activeTextarea.tagName === 'TEXTAREA') {
        // Insert at cursor position
        const cursorPos = activeTextarea.selectionStart;
        const textBefore = activeTextarea.value.substring(0, cursorPos);
        const textAfter = activeTextarea.value.substring(cursorPos);

        activeTextarea.value = textBefore + emoji + textAfter;

        // Move cursor after the inserted emoji
        activeTextarea.selectionStart = cursorPos + emoji.length;
        activeTextarea.selectionEnd = cursorPos + emoji.length;

        // Focus back on the textarea
        activeTextarea.focus();
    } else {
        // If no textarea is focused, try to find one in the active comment form
        const commentForms = document.querySelectorAll('.comment-form');
        if (commentForms.length > 0) {
            // Use the last form as it's likely the one the user is interacting with
            const textarea = commentForms[commentForms.length - 1].querySelector('textarea');
            if (textarea) {
                // Append to the end
                textarea.value += emoji;
                textarea.focus();
            }
        }
    }
}

/**
 * Show a toast notification
 */
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed bottom-4 right-4 z-50';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `p-3 rounded-lg shadow-lg mb-2 flex items-center justify-between transition-all duration-300 transform translate-x-full`;

    // Set background color based on type
    if (type === 'success') {
        toast.classList.add('bg-green-100', 'text-green-800', 'border-l-4', 'border-green-500');
    } else if (type === 'error') {
        toast.classList.add('bg-red-100', 'text-red-800', 'border-l-4', 'border-red-500');
    } else if (type === 'info') {
        toast.classList.add('bg-blue-100', 'text-blue-800', 'border-l-4', 'border-blue-500');
    }

    // Add content
    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'} mr-2"></i>
            <span>${message}</span>
        </div>
        <button class="ml-4 text-gray-500 hover:text-gray-700 focus:outline-none">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to container
    toastContainer.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 10);

    // Add close button functionality
    const closeButton = toast.querySelector('button');
    closeButton.addEventListener('click', () => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            toast.remove();
        }, 300);
    });

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }
    }, 3000);
}
