// Alpine.js Initialization

// Make sure chatApp is defined globally
document.addEventListener('DOMContentLoaded', function() {
    console.log('Alpine initialization script loaded');
    
    // Define the chat app data
    window.chatAppData = {
        // Core properties
        _sessionId: Math.random().toString(36).substring(2, 15),
        username: '',
        newMessage: '',
        messages: [],
        onlineUsers: [],
        currentChatId: '1', // Default to General chat
        currentChatType: 'room',
        currentChatTitle: 'DavarTruth Chat',
        connectionStatus: 'Connecting...',
        isOnline: navigator.onLine,
        showEmojiPicker: false,
        selectedFile: null,
        isRecording: false,
        recorder: null,
        audioChunks: [],
        isTyping: false,
        typingUser: '',
        typingTimeout: null,
        replyingTo: null,
        searchQuery: '',
        showSidebar: window.innerWidth >= 768,
        isMobile: window.innerWidth < 768,
        
        // Offline features
        isOfflineMode: false,
        pendingMessages: [],
        lastSyncTime: null,
        
        // Encryption features
        isEncryptionEnabled: true,
        encryptionKey: null,
        
        // WebRTC features
        isPeerToPeerEnabled: false,
        connectedPeers: [],
        showQRCode: false,
        qrCodeData: null,
        
        // Bible features
        showBiblePanel: false,
        selectedBook: null,
        selectedChapter: null,
        bibleSearchQuery: '',
        bibleSearchResults: [],
        isBibleDataAvailable: false,
        bibleDownloadProgress: 0,

        // Rooms data
        rooms: [
            { id: '1', name: 'General', description: 'General discussion' },
            { id: '2', name: 'Bible Study', description: 'Discuss Bible topics' },
            { id: '3', name: 'Prayer Requests', description: 'Share prayer needs' }
        ],
        
        // Computed properties
        get filteredRooms() {
            if (!this.searchQuery.trim()) return this.rooms;
            const query = this.searchQuery.toLowerCase();
            return this.rooms.filter(room =>
                room.name.toLowerCase().includes(query) ||
                (room.description && room.description.toLowerCase().includes(query))
            );
        },
        
        // Initialization
        init() {
            console.log('Chat app initialized with session ID:', this._sessionId);
            
            // Get username from meta tag if authenticated
            const usernameMeta = document.querySelector('meta[name="user_username"]');
            if (usernameMeta && usernameMeta.content) {
                this.username = usernameMeta.content;
                console.log('Username found from authenticated user:', this.username);
            }
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Check for mobile on resize
            window.addEventListener('resize', () => {
                this.isMobile = window.innerWidth < 768;
                if (!this.isMobile) {
                    this.showSidebar = true;
                }
            });
        },
        
        // Set up event listeners
        setupEventListeners() {
            // Online/offline status
            window.addEventListener('online', () => {
                this.isOnline = true;
                this.connectionStatus = 'Online';
            });
            
            window.addEventListener('offline', () => {
                this.isOnline = false;
                this.connectionStatus = 'Offline';
            });
        },
        
        // Format time
        formatTime(date) {
            if (!date) return '';
            
            const now = new Date();
            const messageDate = new Date(date);
            
            // Check if same day
            if (now.toDateString() === messageDate.toDateString()) {
                return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }
            
            // Check if yesterday
            const yesterday = new Date(now);
            yesterday.setDate(now.getDate() - 1);
            if (yesterday.toDateString() === messageDate.toDateString()) {
                return 'Yesterday ' + messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            }
            
            // Otherwise show date
            return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ' ' + 
                   messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        },
        
        // Scroll to bottom of messages
        scrollToBottom() {
            const container = document.getElementById('chat-messages');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        },
        
        // Toggle emoji picker
        toggleEmojiPicker() {
            this.showEmojiPicker = !this.showEmojiPicker;
        },
        
        // Add emoji to message
        addEmoji(emoji) {
            this.newMessage += emoji;
            this.showEmojiPicker = false;
        },
        
        // Send message
        sendMessage() {
            if (!this.username.trim()) {
                alert('Please enter your name to join the chat');
                return;
            }
            
            if (!this.newMessage.trim()) {
                return;
            }
            
            // Create message object
            const messageId = 'msg-' + Date.now();
            const message = {
                id: messageId,
                username: this.username,
                content: this.newMessage.trim(),
                timestamp: new Date(),
                status: 'sent'
            };
            
            // Add to messages array
            this.messages.push(message);
            
            // Clear input
            this.newMessage = '';
            
            // Scroll to bottom
            this.scrollToBottom();
        },
        
        // Open chat room
        openChatRoom(roomId, roomName) {
            this.currentChatType = 'room';
            this.currentChatId = roomId;
            this.currentChatTitle = roomName;
            
            // On mobile, hide sidebar after selecting a room
            if (this.isMobile) {
                this.showSidebar = false;
            }
        },
        
        // Toggle Bible panel
        toggleBiblePanel() {
            this.showBiblePanel = !this.showBiblePanel;
        }
    };
    
    // Register Alpine.js data
    if (typeof Alpine !== 'undefined') {
        Alpine.data('chatApp', () => window.chatAppData);
    } else {
        console.error('Alpine.js not loaded yet');
    }
});
