/**
 * Alpine.js Components Fix
 * This script ensures all Alpine.js components are properly initialized
 * and fixes issues with missing functions and variables
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Alpine components fix loaded');
    
    // Check if Alpine.js is loaded
    if (typeof window.Alpine === 'undefined') {
        console.error('Alpine.js not loaded. Loading it now...');
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js';
        script.defer = true;
        script.onload = initializeAlpineComponents;
        document.head.appendChild(script);
    } else {
        // Alpine.js is already loaded, initialize components
        initializeAlpineComponents();
    }
    
    function initializeAlpineComponents() {
        console.log('Initializing Alpine.js components');
        
        // Define breakReminder component if not already defined
        if (!window.Alpine.data('breakReminder')) {
            window.Alpine.data('breakReminder', function() {
                return {
                    showBreakReminder: false,
                    breakReminderInterval: 60 * 60 * 1000, // 1 hour
                    breakReminderTimer: null,
                    lastBreakTime: null,
                    
                    init() {
                        this.initBreakReminder();
                    },
                    
                    initBreakReminder() {
                        const lastBreak = localStorage.getItem('lastBreakTime');
                        if (lastBreak) {
                            this.lastBreakTime = parseInt(lastBreak);
                            const timeSinceLastBreak = Date.now() - this.lastBreakTime;
                            
                            if (timeSinceLastBreak < this.breakReminderInterval) {
                                this.breakReminderTimer = setTimeout(() => 
                                    this.showBreakReminder = true, 
                                    this.breakReminderInterval - timeSinceLastBreak
                                );
                            } else {
                                this.showBreakReminder = true;
                            }
                        } else {
                            this.breakReminderTimer = setTimeout(() => 
                                this.showBreakReminder = true, 
                                this.breakReminderInterval
                            );
                        }
                    },
                    
                    takeBreak() {
                        this.showBreakReminder = false;
                        this.lastBreakTime = Date.now();
                        localStorage.setItem('lastBreakTime', this.lastBreakTime);
                        clearTimeout(this.breakReminderTimer);
                        this.breakReminderTimer = setTimeout(() => 
                            this.showBreakReminder = true, 
                            this.breakReminderInterval
                        );
                    },
                    
                    continueReading() {
                        this.showBreakReminder = false;
                        clearTimeout(this.breakReminderTimer);
                        this.breakReminderTimer = setTimeout(() => 
                            this.showBreakReminder = true, 
                            15 * 60 * 1000 // 15 minutes
                        );
                    }
                };
            });
        }
        
        // Define bibleVerse component if not already defined
        if (!window.Alpine.data('bibleVerse')) {
            window.Alpine.data('bibleVerse', function() {
                return {
                    showBibleModal: false,
                    currentPostId: null,
                    bibleVerses: [],
                    searchQuery: '',
                    isLoading: false,
                    
                    searchBibleVerses() {
                        if (!this.searchQuery.trim()) return;
                        
                        this.isLoading = true;
                        this.bibleVerses = [];
                        
                        // Fetch Bible verses from API
                        fetch(`/api/bible/search?q=${encodeURIComponent(this.searchQuery)}`)
                            .then(response => response.json())
                            .then(data => {
                                this.bibleVerses = data.verses || [];
                                this.isLoading = false;
                            })
                            .catch(error => {
                                console.error('Error searching Bible verses:', error);
                                this.isLoading = false;
                                
                                // Fallback for offline or error
                                if (this.searchQuery.toLowerCase().includes('john 3:16')) {
                                    this.bibleVerses = [{
                                        id: 'john-3-16',
                                        reference: 'John 3:16',
                                        text: 'For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life.'
                                    }];
                                } else {
                                    this.bibleVerses = [];
                                }
                            });
                    },
                    
                    insertVerseToPost(verse) {
                        if (!verse) return;
                        
                        const commentBox = document.querySelector(`#comment-box-${this.currentPostId}`);
                        if (commentBox) {
                            const verseText = `"${verse.text}" - ${verse.reference}`;
                            commentBox.value += commentBox.value ? `\n\n${verseText}` : verseText;
                            this.showBibleModal = false;
                        }
                    }
                };
            });
        }
        
        // Define churchEvents component if not already defined
        if (!window.Alpine.data('churchEvents')) {
            window.Alpine.data('churchEvents', function() {
                return {
                    events: [],
                    showAddEventModal: false,
                    newEvent: {
                        title: '',
                        date: '',
                        description: '',
                        link: '',
                        linkText: 'Learn More',
                        expiryDate: ''
                    },
                    
                    init() {
                        // Load events from localStorage
                        const storedEvents = localStorage.getItem('churchEvents');
                        this.events = storedEvents ? JSON.parse(storedEvents) : [];
                        
                        // Check for expired events
                        this.removeExpiredEvents();
                    },
                    
                    get filteredEvents() {
                        // Filter out expired events
                        const today = new Date().toISOString().split('T')[0];
                        return this.events.filter(event => event.expiryDate >= today);
                    },
                    
                    formatDate(dateString) {
                        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
                        return new Date(dateString).toLocaleDateString(undefined, options);
                    },
                    
                    addEvent() {
                        // Generate a unique ID
                        const id = Date.now();
                        
                        // Add the new event
                        this.events.push({
                            id,
                            title: this.newEvent.title,
                            date: this.newEvent.date,
                            description: this.newEvent.description,
                            link: this.newEvent.link || '#',
                            linkText: this.newEvent.linkText,
                            expiryDate: this.newEvent.expiryDate
                        });
                        
                        // Save to localStorage
                        this.saveEvents();
                        
                        // Reset form and close modal
                        this.resetEventForm();
                        this.showAddEventModal = false;
                    },
                    
                    removeEvent(id) {
                        this.events = this.events.filter(event => event.id !== id);
                        this.saveEvents();
                    },
                    
                    removeExpiredEvents() {
                        const today = new Date().toISOString().split('T')[0];
                        const expired = this.events.filter(event => event.expiryDate < today);
                        if (expired.length > 0) {
                            console.log(`Removed ${expired.length} expired events`);
                            this.events = this.events.filter(event => event.expiryDate >= today);
                            this.saveEvents();
                        }
                    },
                    
                    saveEvents() {
                        localStorage.setItem('churchEvents', JSON.stringify(this.events));
                    },
                    
                    resetEventForm() {
                        this.newEvent = {
                            title: '',
                            date: '',
                            description: '',
                            link: '',
                            linkText: 'Register for Event',
                            expiryDate: ''
                        };
                    }
                };
            });
        }
        
        // Define churchAdverts component if not already defined
        if (!window.Alpine.data('churchAdverts')) {
            window.Alpine.data('churchAdverts', function() {
                return {
                    adverts: [],
                    showAddAdvertModal: false,
                    newAdvert: {
                        title: '',
                        description: '',
                        link: '',
                        linkText: 'View Announcement Details',
                        expiryDate: ''
                    },
                    
                    init() {
                        // Load adverts from localStorage
                        const storedAdverts = localStorage.getItem('churchAdverts');
                        this.adverts = storedAdverts ? JSON.parse(storedAdverts) : [];
                        
                        // Check for expired adverts
                        this.removeExpiredAdverts();
                    },
                    
                    get filteredAdverts() {
                        // Filter out expired adverts
                        const today = new Date().toISOString().split('T')[0];
                        return this.adverts.filter(advert => advert.expiryDate >= today);
                    },
                    
                    addAdvert() {
                        // Generate a unique ID
                        const id = Date.now();
                        
                        // Add the new advert
                        this.adverts.push({
                            id,
                            title: this.newAdvert.title,
                            description: this.newAdvert.description,
                            link: this.newAdvert.link || '#',
                            linkText: this.newAdvert.linkText,
                            expiryDate: this.newAdvert.expiryDate
                        });
                        
                        // Save to localStorage
                        this.saveAdverts();
                        
                        // Reset form and close modal
                        this.resetAdvertForm();
                        this.showAddAdvertModal = false;
                    },
                    
                    removeAdvert(id) {
                        this.adverts = this.adverts.filter(advert => advert.id !== id);
                        this.saveAdverts();
                    },
                    
                    removeExpiredAdverts() {
                        const today = new Date().toISOString().split('T')[0];
                        const expired = this.adverts.filter(advert => advert.expiryDate < today);
                        if (expired.length > 0) {
                            console.log(`Removed ${expired.length} expired announcements`);
                            this.adverts = this.adverts.filter(advert => advert.expiryDate >= today);
                            this.saveAdverts();
                        }
                    },
                    
                    saveAdverts() {
                        localStorage.setItem('churchAdverts', JSON.stringify(this.adverts));
                    },
                    
                    resetAdvertForm() {
                        this.newAdvert = {
                            title: '',
                            description: '',
                            link: '',
                            linkText: 'View Announcement Details',
                            expiryDate: ''
                        };
                    }
                };
            });
        }
    }
});
