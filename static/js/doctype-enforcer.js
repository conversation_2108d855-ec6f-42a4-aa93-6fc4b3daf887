/**
 * DOCTYPE Enforcer Script
 * 
 * This script ensures that all iframes in the document have a proper DOCTYPE declaration
 * to prevent quirks mode rendering.
 */
(function() {
    // Function to log information about document mode
    function logDocumentMode() {
        console.log('Main document mode:', document.compatMode);
        if (document.compatMode === 'BackCompat') {
            console.warn('Main document is in Quirks Mode!');
        } else {
            console.log('Main document is in Standards Mode.');
        }
    }

    // Function to fix iframes without DOCTYPE
    function fixIframes() {
        const iframes = document.querySelectorAll('iframe');
        console.log(`Found ${iframes.length} iframes to check`);
        
        iframes.forEach((iframe, index) => {
            try {
                // Wait for iframe to load
                iframe.addEventListener('load', function() {
                    try {
                        const iframeDoc = iframe.contentDocument;
                        
                        // Check if we can access the iframe (same-origin policy)
                        if (iframeDoc) {
                            console.log(`iframe #${index} mode:`, iframeDoc.compatMode);
                            
                            // If in quirks mode, try to fix it
                            if (iframeDoc.compatMode === 'BackCompat') {
                                console.warn(`iframe #${index} is in Quirks Mode!`);
                                
                                // Try to add DOCTYPE if possible
                                if (iframe.srcdoc) {
                                    // If using srcdoc attribute
                                    if (!iframe.srcdoc.trim().startsWith('<!DOCTYPE html>')) {
                                        iframe.srcdoc = '<!DOCTYPE html>' + iframe.srcdoc;
                                        console.log(`Added DOCTYPE to iframe #${index} srcdoc`);
                                    }
                                } else if (iframeDoc.documentElement) {
                                    // If we can modify the document directly
                                    try {
                                        // Get the HTML content
                                        const html = iframeDoc.documentElement.outerHTML;
                                        
                                        // Create a new document with DOCTYPE
                                        iframeDoc.open();
                                        iframeDoc.write('<!DOCTYPE html>' + html);
                                        iframeDoc.close();
                                        
                                        console.log(`Added DOCTYPE to iframe #${index} document`);
                                    } catch (e) {
                                        console.error(`Cannot modify iframe #${index} content:`, e);
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.log(`Cannot access iframe #${index} due to same-origin policy`);
                    }
                });
                
                // For iframes that are already loaded
                if (iframe.contentDocument && iframe.contentWindow.document.readyState === 'complete') {
                    const event = new Event('load');
                    iframe.dispatchEvent(event);
                }
            } catch (e) {
                console.log(`Error processing iframe #${index}:`, e);
            }
        });
    }

    // Run when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            logDocumentMode();
            fixIframes();
        });
    } else {
        logDocumentMode();
        fixIframes();
    }
    
    // Also run when window is fully loaded (for dynamically added iframes)
    window.addEventListener('load', function() {
        fixIframes();
        
        // Monitor for dynamically added iframes
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length) {
                    let newIframes = false;
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeName === 'IFRAME') {
                            newIframes = true;
                        } else if (node.querySelectorAll) {
                            const iframes = node.querySelectorAll('iframe');
                            if (iframes.length > 0) {
                                newIframes = true;
                            }
                        }
                    });
                    
                    if (newIframes) {
                        console.log('New iframes detected, fixing...');
                        fixIframes();
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
})();
