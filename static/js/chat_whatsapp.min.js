function chatApp(){return{_sessionId:Math.random().toString(36).substring(2,15),username:"",newMessage:"",messages:[],onlineUsers:[],currentChatId:"1",currentChatType:"room",currentChatTitle:"Davar<PERSON>ruth Chat",connectionStatus:"Connecting...",isOnline:navigator.onLine,showEmojiPicker:!1,selectedFile:null,selectedFilePreview:null,imageCaption:"",showImagePreview:!1,isRecording:!1,recorder:null,audioChunks:[],isTyping:!1,typingUser:"",typingTimeout:null,replyingTo:null,searchQuery:"",showSidebar:window.innerWidth>=768,isMobile:window.innerWidth<768,selectedContact:null,showHeaderMenu:!1,showProfileMenu:!1,showNewGroupModal:!1,showStatusModal:!1,showSettingsModal:!1,showLoginPrompt:!1,showContactsModal:!1,isDarkMode:"true"===localStorage.getItem("darkMode"),showBlockUserModal:!1,showPhoneNumberModal:!1,showDeleteAccountModal:!1,showInviteFriendModal:!1,blockedUsers:[],usersToBlock:[],blockUserSearch:"",sendAsSMS:"true"===localStorage.getItem("sendAsSMS"),smsDeliveryReport:"true"===localStorage.getItem("smsDeliveryReport"),isEncryptionEnabled:"false"!==localStorage.getItem("isEncryptionEnabled"),currentPhone:"",newPhone:"",verificationCode:"",codeSent:!1,deleteConfirmation:"",inviteEmail:"",inviteMessage:"",showChatSearch:!1,chatSearchQuery:"",chatSearchResults:[],showBiblePanel:!1,selectedBook:null,selectedChapter:null,bibleVerses:[],rooms:[{id:"1",name:"General",description:"General discussion"},{id:"2",name:"Bible Study",description:"Discuss Bible topics"},{id:"3",name:"Prayer Requests",description:"Share prayer needs"}],contacts:[],contacts:[],get filteredRooms(){if(!this.searchQuery.trim())return this.rooms;const e=this.searchQuery.toLowerCase();return this.rooms.filter(t=>t.name.toLowerCase().includes(e)||t.description&&t.description.toLowerCase().includes(e))},init(){console.log("WhatsApp-style chat app initialized with session ID:",this._sessionId);const e=document.querySelector('meta[name="user_username"]');e&&e.content&&(this.username=e.content,console.log("Username found from authenticated user:",this.username));const t=this.getRoomIdFromUrl();if(t){this.currentChatId=t;const e=this.rooms.find(e=>e.id===t);e&&(this.currentChatTitle=e.name)}this.isDarkMode&&document.documentElement.classList.add("dark-mode"),this.setupEventListeners(),this.initIndexedDB().then(()=>{this.loadMessages(),this.connectWebSocket(),this.initVoiceRecorder(),this.username.trim()&&this.$nextTick(()=>{this.focusMessageInput()})}),document.addEventListener("visibilitychange",()=>{"visible"===document.visibilityState&&this.username.trim()&&this.focusMessageInput()}),window.addEventListener("resize",()=>{const e=this.isMobile;this.isMobile=window.innerWidth<768,e&&!this.isMobile?this.showSidebar=!0:!e&&this.isMobile&&(this.currentChatId?this.showSidebar=!1:this.showSidebar=!0),console.log("Resize detected. Mobile:",this.isMobile,"Show sidebar:",this.showSidebar)}),this.isMobile=window.innerWidth<768,this.isMobile&&this.currentChatId&&"1"!==this.currentChatId?this.showSidebar=!1:this.showSidebar=!0,console.log("Initial mobile check. Mobile:",this.isMobile,"Show sidebar:",this.showSidebar)},setupEventListeners(){window.addEventListener("online",()=>{this.isOnline=!0,this.connectionStatus="Online",this.connectWebSocket()}),window.addEventListener("offline",()=>{this.isOnline=!1,this.connectionStatus="Offline"}),document.addEventListener("click",e=>{this.showEmojiPicker&&!e.target.closest(".wa-emoji-picker")&&!e.target.closest(".wa-emoji-btn")&&(this.showEmojiPicker=!1),this.showHeaderMenu&&!e.target.closest(".wa-header-menu")&&!e.target.closest(".fa-ellipsis-v")&&(this.showHeaderMenu=!1),this.showProfileMenu&&!e.target.closest(".wa-profile-menu")&&!e.target.closest(".wa-user-avatar")&&(this.showProfileMenu=!1)}),document.addEventListener("keydown",e=>{"Escape"===e.key&&this.closeAllModals()}),document.addEventListener("contact-selected",e=>{e.detail&&e.detail.contact&&this.handleContactSelected(e.detail.contact)}),document.addEventListener("close-contacts-modal",()=>{console.log("Received close-contacts-modal event"),this.closeContactsModal()})},getRoomIdFromUrl(){const e=window.location.pathname,t=e.match(/\/chat\/(\d+)/);return t?t[1]:"1"},async initIndexedDB(){console.log("Initializing IndexedDB...");try{return this.db=await idb.openDB("chatApp",1,{upgrade(e){e.objectStoreNames.contains("messages")||e.createObjectStore("messages",{keyPath:"id"})}}),console.log("IndexedDB initialized successfully"),!0}catch(e){return console.error("Error initializing IndexedDB:",e),!1}},async loadMessages(){console.log("Loading messages..."),console.log("Room ID:",this.currentChatId);try{if(this.isOnline){console.log("Online, fetching messages from server...");const e=await fetch(`/chat/messages/${this.currentChatId}`);if(e.ok){const t=await e.json();this.messages=Array.isArray(t)?t:t.messages||[];const s=this.messages.map(e=>({...e,timestamp:e instanceof Date?e.timestamp.toISOString():e.timestamp}));try{const e=this.db.transaction("messages","readwrite"),t=e.objectStore("messages");for(const e of s)await t.put(e);await e.done,console.log("Stored",s.length,"messages in IndexedDB")}catch(e){console.error("Error storing messages in IndexedDB:",e)}console.log("Loaded",this.messages.length,"messages from server")}else console.error("Failed to fetch messages from server"),await this.loadMessagesFromIndexedDB()}else await this.loadMessagesFromIndexedDB()}catch(e){console.error("Error loading messages:",e),await this.loadMessagesFromIndexedDB()}console.log("Messages after loading:",this.messages.length),this.$nextTick(()=>{this.scrollToBottom()})},async loadMessagesFromIndexedDB(){console.log("Loading messages from IndexedDB...");try{const e=this.db.transaction("messages","readonly"),t=e.objectStore("messages");let s=await t.getAll();Array.isArray(s)||(console.warn("getAll() did not return an array, creating empty array"),s=[]),this.messages=s.filter(e=>e&&e.roomId===this.currentChatId),this.messages=this.messages.map(e=>({...e,timestamp:"string"==typeof e.timestamp?new Date(e.timestamp):e.timestamp})),console.log("Loaded",this.messages.length,"messages from IndexedDB")}catch(e){console.error("Error loading messages from IndexedDB:",e),this.messages=[]}},resetWebSocket(){this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.reconnectAttempts=0,this.socket&&(try{this.socket.onclose=null,this.socket.onerror=null,this.socket.onmessage=null,this.socket.onopen=null,this.socket.readyState!==WebSocket.OPEN&&this.socket.readyState!==WebSocket.CONNECTING||this.socket.close()}catch(e){console.error("Error closing WebSocket:",e)}this.socket=null},connectWebSocket(){if(!this.isOnline)return console.log("Offline, not connecting to WebSocket"),void(this.connectionStatus="Offline");console.log("Online, connecting to WebSocket..."),this.resetWebSocket();const e=this.getAuthToken();if(!e)return console.log("No access token found, running in read-only mode"),this.connectionStatus="Please log in to send messages",this.addSystemMessage("You are not logged in. Please log in to send messages."),void(this.showLoginPrompt=!0);const t="https:"===window.location.protocol?"wss:":"ws:",s=`${t}//${window.location.host}/ws/chat/${this.currentChatId}?token=${encodeURIComponent(e)}`;console.log("Room ID for WebSocket:",this.currentChatId),console.log("Connecting to WebSocket with token");try{this.socket=new WebSocket(s)}catch(e){return console.error("Error creating WebSocket:",e),this.connectionStatus="Connection Failed",void this.addSystemMessage("Failed to connect to chat server. Please try again later.")}this.socket.onopen=()=>{console.log("WebSocket connected"),this.connectionStatus="Connected",this.socket.send(JSON.stringify({type:"join",username:this.username,room_id:this.currentChatId}))},this.socket.onmessage=e=>{try{const t=JSON.parse(e.data);switch(console.log("WebSocket message received:",t),t.type){case"message":case"chat_message":this.handleIncomingMessage(t);break;case"online_users":case"user_list":this.onlineUsers=t.users||[],console.log("Users in chat:",this.onlineUsers);break;case"typing":this.handleTypingIndicator(t);break;case"system_message":this.addSystemMessage(t.content);break;default:console.log("Unknown message type:",t.type,t)}}catch(e){console.error("Error processing WebSocket message:",e)}},this.socket.onclose=()=>{console.log("WebSocket disconnected"),this.connectionStatus="Disconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectAttempts=(this.reconnectAttempts||0)+1;const e=Math.min(3e4,1e3*Math.pow(1.5,this.reconnectAttempts));console.log(`Will attempt to reconnect in ${e/1e3} seconds (attempt ${this.reconnectAttempts})`),this.reconnectTimer=setTimeout(()=>{this.isOnline&&(console.log("Online, connecting to WebSocket..."),this.connectWebSocket())},e)},this.socket.onerror=e=>{console.error("WebSocket error:",e),this.connectionStatus="Connection Error"}},getAuthToken(){const e=document.querySelector('meta[name="is_authenticated"]');if(e&&"True"===e.content)return console.log("User is authenticated via meta tag"),"authenticated_via_meta";let t=null;const s=document.cookie.replace(/(?:(?:^|.*;\s*)access_token\s*\=\s*([^;]*).*$)|^.*$/,"$1");return s&&(t=s.startsWith("Bearer ")?s.substring(7):s),t||(t=localStorage.getItem("access_token")||sessionStorage.getItem("access_token")),t||(t=document.cookie.replace(/(?:(?:^|.*;\s*)jwt\s*\=\s*([^;]*).*$)|^.*$/,"$1")),!t&&this.username&&(console.log("Creating guest token for username:",this.username),t=`guest_${this.username}_${Date.now()}`),console.log("Found token:",t?`Yes (length: ${t.length})`:"No"),t},async handleIncomingMessage(e){if(!this.messages.find(t=>t.id===e.id)){const t={id:e.id,username:e.username,content:e.content,timestamp:new Date(e.timestamp),roomId:this.currentChatId,isImage:e.content.includes("<img"),isBibleVerse:e.content.includes('class="bible-verse"'),isSystem:"System"===e.username,user:e.user||null};this.messages.push(t);try{const s=this.db.transaction("messages","readwrite"),i=s.objectStore("messages"),a={...t,timestamp:t.timestamp.toISOString(),user:t.user?JSON.parse(JSON.stringify(t.user)):null};await i.put(a),await s.done}catch(e){console.error("Error storing message in IndexedDB:",e)}this.$nextTick(()=>{this.scrollToBottom()}),e.username!==this.username&&this.playNotificationSound()}},handleTypingIndicator(e){e.username&&e.username!==this.username&&(this.isTyping=!0,this.typingUser=e.username,this.typingTimeout&&clearTimeout(this.typingTimeout),this.typingTimeout=setTimeout(()=>{this.isTyping=!1,this.typingUser=""},3e3))},addSystemMessage(e){const t={id:"system-"+Date.now(),username:"System",content:e,timestamp:new Date,roomId:this.currentChatId,isSystem:!0};this.messages.push(t);try{const e=this.db.transaction("messages","readwrite"),s=e.objectStore("messages"),i={...t,timestamp:t.timestamp.toISOString()};s.put(i),e.done.catch(e=>console.error("Error storing system message:",e))}catch(e){console.error("Error storing system message in IndexedDB:",e)}this.$nextTick(()=>{this.scrollToBottom()})},async sendMessage(e=null,t=!1,s=!1){if(!this.username.trim())return void alert("Please enter your name to join the chat");if((!this.newMessage.trim()&&!this.selectedFile||!this.isOnline)&&!e)return;if(!this.getAuthToken())return this.showLoginPrompt=!0,void this.addSystemMessage("You need to be logged in to send messages.");const i="msg-"+Date.now();let a=e||this.newMessage.trim(),n=!1,o="";this.selectedFile&&!e&&(this.showImagePreview||(this.showImagePreview=!0),n)&&a&&(a=`<div class="message-text">${a}</div>${o}`),n&&(a=o);const r={id:i,username:this.username,content:a,timestamp:new Date,roomId:this.currentChatId,pending:!0,isImage:s||a.includes("<img"),isBibleVerse:t,status:"sent"};this.replyingTo&&(r.replyTo={id:this.replyingTo.id,username:this.replyingTo.username,content:this.replyingTo.content}),this.messages.push(r),this.newMessage="",this.selectedFile=null,this.replyingTo=null,this.$nextTick(()=>{this.scrollToBottom()});if(this.currentChatId.startsWith("external_"))console.log("Sending message to external contact, storing locally");else if(this.socket&&this.socket.readyState===WebSocket.OPEN){this.socket.send(JSON.stringify({type:"message",id:i,username:this.username,content:a,replyTo:r.replyTo})),setTimeout(()=>{const e=this.messages.findIndex(e=>e.id===i);-1!==e&&(this.messages[e].pending=!1,this.messages[e].status="delivered",setTimeout(()=>{const e=this.messages.findIndex(e=>e.id===i);-1!==e&&(this.messages[e].status="read")},2e3))},1e3)}else try{const e=this.db.transaction("messages","readwrite"),t=e.objectStore("messages"),s={...r,timestamp:r.timestamp.toISOString(),replyTo:r.replyTo?JSON.parse(JSON.stringify(r.replyTo)):null};await t.put(s),await e.done,console.log("Message stored offline"),this.addSystemMessage("You are offline. Message will be sent when you reconnect.")}catch(e){console.error("Error storing offline message:",e)}},async uploadFile(e){console.log("Uploading file:",e.name,"type:",e.type);const t=new FormData;t.append("file",e);try{const s=await fetch("/api/upload",{method:"POST",body:t});if(!s.ok){const e=await s.text();throw console.error("Upload failed:",e),new Error(`Failed to upload file: ${e}`)}const i=await s.json();return console.log("Upload successful, URL:",i.url),i.url}catch(e){throw console.error("Error in uploadFile:",e),e}},handleFileUpload(e){const t=e.target.files[0];if(!t)return void console.log("No file selected");if(console.log("File selected:",t.name,"type:",t.type,"size:",t.size),!t.type.startsWith("image/"))return alert("Only image files are supported"),void(e.target.value="");if(t.size>5242880)return alert("File size should be less than 5MB"),void(e.target.value="");this.selectedFile=t,this.imageCaption="",this.selectedFilePreview=URL.createObjectURL(t),this.showImagePreview=!0,console.log("File ready to preview:",this.selectedFile.name)},cancelImageUpload(){this.$refs.fileInput&&(this.$refs.fileInput.value=""),this.selectedFile=null,this.selectedFilePreview&&(URL.revokeObjectURL(this.selectedFilePreview),this.selectedFilePreview=null),this.imageCaption="",this.showImagePreview=!1},async sendImageWithCaption(){if(!this.selectedFile)return console.error("No file selected"),void(this.showImagePreview=!1);try{const e=await this.uploadFile(this.selectedFile);let t=`<div class="message-image"><img src="${e}" alt="Uploaded image" style="max-width: 100%; border-radius: 8px;"></div>`;this.imageCaption.trim()&&(t=`<div class="message-text">${this.imageCaption}</div>${t}`),await this.sendMessage(t,!1,!0),this.cancelImageUpload()}catch(e){console.error("Error sending image with caption:",e),alert("Failed to send image. Please try again.")}},async initVoiceRecorder(){try{window.VoiceRecorder||(console.log("Voice recorder not found, loading script..."),await this.loadScript("/static/js/voice-recorder.js")),this.recorder=new VoiceRecorder,await this.recorder.initialize()}catch(e){console.error("Failed to initialize voice recorder",e)}},loadScript(e){return new Promise((t,s)=>{const i=document.createElement("script");i.src=e,i.onload=t,i.onerror=s,document.head.appendChild(i)})},async startVoiceInput(){if(!this.recorder)return void alert("Voice recorder not available");try{this.isRecording=!0,await this.recorder.startRecording();const e=document.querySelector(".wa-voice-record-btn");e&&e.classList.add("recording")}catch(e){console.error("Error starting recording:",e),this.isRecording=!1}},async stopVoiceInput(){if(!this.recorder||!this.isRecording)return;try{const e=await this.recorder.stopRecording();this.isRecording=!1;const t=document.querySelector(".wa-voice-record-btn");t&&t.classList.remove("recording");const s=new FormData;s.append("file",e,"voice-message.webm");const i=await fetch("/api/upload",{method:"POST",body:s});if(!i.ok)throw new Error("Failed to upload voice message");const a=await i.json();this.newMessage=`<audio controls src="${a.url}"></audio>`,this.sendMessage()}catch(e){console.error("Error stopping recording:",e)}},toggleEmojiPicker(){this.showEmojiPicker=!this.showEmojiPicker},addEmoji(e){this.newMessage+=e,this.showEmojiPicker=!1,this.$refs.messageInput.focus()},replyToMessage(e){this.replyingTo=e,this.$refs.messageInput.focus()},cancelReply(){this.replyingTo=null},formatTime(e){if(!e)return"";const t=new Date,s=new Date(e);if(t.toDateString()===s.toDateString())return s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});const i=new Date(t);return i.setDate(t.getDate()-1),i.toDateString()===s.toDateString()?"Yesterday "+s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):s.toLocaleDateString([],{month:"short",day:"numeric"})+" "+s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},scrollToBottom(){const e=document.getElementById("chat-messages");e&&(e.scrollTop=e.scrollHeight)},focusMessageInput(){this.username.trim()&&this.$refs.messageInput&&setTimeout(()=>{if(this.$refs.messageInput.focus(),/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){this.$refs.messageInput.click()}},300)},playNotificationSound(){try{(new Audio("/static/sounds/notification.mp3")).play()}catch(e){console.error("Error playing notification sound:",e)}},openChatRoom(e,t){console.log("Opening chat room:",e,t),this.currentChatType="room",this.currentChatId=e,this.currentChatTitle=t,history.pushState&&history.pushState({path:`/chat/${e}`},"",`/chat/${e}`),this.loadMessages(),this.connectWebSocket(),this.isMobile&&(console.log("Mobile device detected, hiding sidebar"),this.showSidebar=!1,setTimeout(()=>{const e=document.querySelector(".wa-chat-area");e&&e.classList.remove("wa-hidden")},100)),this.$nextTick(()=>{this.focusMessageInput()})},closeAllModals(){this.showHeaderMenu=!1,this.showProfileMenu=!1,this.showNewGroupModal=!1,this.showStatusModal=!1,this.showSettingsModal=!1,this.showEmojiPicker=!1,this.showChatSearch=!1,this.showBiblePanel=!1,this.showLoginPrompt=!1,this.showContactsModal=!1,this.showImagePreview=!1,this.showBlockUserModal=!1,this.showPhoneNumberModal=!1,this.showDeleteAccountModal=!1,this.showInviteFriendModal=!1,this.replyingTo=null,console.log("All modals closed")},toggleHeaderMenu(){console.log("Toggling header menu"),this.showHeaderMenu=!this.showHeaderMenu,this.showProfileMenu=!1,this.showEmojiPicker=!1,this.showContactsModal=!1,this.showNewGroupModal=!1,this.showStatusModal=!1,this.showHeaderMenu&&setTimeout(()=>{const e=document.querySelector(".wa-header-menu");e&&e.classList.add("active")},10)},toggleDarkMode(){this.isDarkMode=!this.isDarkMode,localStorage.setItem("darkMode",this.isDarkMode),this.isDarkMode?document.documentElement.classList.add("dark-mode"):document.documentElement.classList.remove("dark-mode"),this.showProfileMenu=!1},openContactsModal(){console.log("Opening contacts modal"),this.showContactsModal=!0,document.dispatchEvent(new CustomEvent("load-contacts"))}}}
