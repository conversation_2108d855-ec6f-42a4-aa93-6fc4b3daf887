/**
 * HTML Validator Script
 * 
 * This script checks for unclosed HTML tags and other structural issues
 * that might cause "Unexpected end of input" errors.
 */

(function() {
    // Function to validate HTML structure
    function validateHtmlStructure() {
        // Get the HTML content
        const htmlContent = document.documentElement.outerHTML;
        
        // Check for unclosed tags
        const unclosedTags = findUnclosedTags(htmlContent);
        
        if (unclosedTags.length > 0) {
            console.error('Found unclosed HTML tags:');
            unclosedTags.forEach(tag => {
                console.error(`- ${tag.tagName} at position ${tag.position}`);
            });
            
            // Create a notification for the user
            createUnclosedTagsNotification(unclosedTags);
            
            // Try to fix the unclosed tags
            fixUnclosedTags(unclosedTags);
        } else {
            console.log('All HTML tags are properly closed');
        }
        
        // Check for unclosed script tags
        const unclosedScripts = findUnclosedScriptTags(htmlContent);
        
        if (unclosedScripts.length > 0) {
            console.error('Found unclosed script tags:');
            unclosedScripts.forEach(script => {
                console.error(`- Script tag at position ${script.position}`);
            });
            
            // Create a notification for the user
            createUnclosedScriptsNotification(unclosedScripts);
            
            // Try to fix the unclosed script tags
            fixUnclosedScriptTags(unclosedScripts);
        } else {
            console.log('All script tags are properly closed');
        }
        
        // Check for unclosed JavaScript blocks
        checkJavaScriptBlocks();
    }
    
    // Function to find unclosed HTML tags
    function findUnclosedTags(htmlContent) {
        const unclosedTags = [];
        const tagStack = [];
        const tagRegex = /<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;
        
        let match;
        while ((match = tagRegex.exec(htmlContent)) !== null) {
            const fullTag = match[0];
            const tagName = match[1].toLowerCase();
            const position = match.index;
            
            // Skip self-closing tags
            if (fullTag.endsWith('/>') || ['meta', 'link', 'img', 'br', 'hr', 'input'].includes(tagName)) {
                continue;
            }
            
            // Check if it's an opening or closing tag
            if (fullTag.startsWith('</')) {
                // Closing tag
                if (tagStack.length === 0) {
                    // Extra closing tag
                    unclosedTags.push({ tagName, position, type: 'extra-closing' });
                } else if (tagStack[tagStack.length - 1].tagName === tagName) {
                    // Matching closing tag
                    tagStack.pop();
                } else {
                    // Mismatched closing tag
                    unclosedTags.push({ 
                        tagName: tagStack[tagStack.length - 1].tagName, 
                        position: tagStack[tagStack.length - 1].position,
                        type: 'unclosed'
                    });
                    tagStack.pop();
                }
            } else {
                // Opening tag
                tagStack.push({ tagName, position });
            }
        }
        
        // Any tags left in the stack are unclosed
        tagStack.forEach(tag => {
            unclosedTags.push({ ...tag, type: 'unclosed' });
        });
        
        return unclosedTags;
    }
    
    // Function to find unclosed script tags
    function findUnclosedScriptTags(htmlContent) {
        const unclosedScripts = [];
        const scriptRegex = /<script\b[^>]*>([\s\S]*?)(?:<\/script>|$)/gi;
        
        let match;
        while ((match = scriptRegex.exec(htmlContent)) !== null) {
            const fullScript = match[0];
            const scriptContent = match[1];
            const position = match.index;
            
            // Check if the script tag is closed
            if (!fullScript.endsWith('</script>')) {
                unclosedScripts.push({ position, content: scriptContent });
            }
        }
        
        return unclosedScripts;
    }
    
    // Function to check for unclosed JavaScript blocks
    function checkJavaScriptBlocks() {
        const scripts = document.querySelectorAll('script:not([src])');
        
        scripts.forEach(script => {
            const scriptContent = script.textContent;
            
            try {
                // Try to parse the script content
                new Function(scriptContent);
            } catch (error) {
                console.error(`Syntax error in inline script:`, error.message);
                
                // Check for common issues
                const openBraces = (scriptContent.match(/\{/g) || []).length;
                const closeBraces = (scriptContent.match(/\}/g) || []).length;
                
                if (openBraces !== closeBraces) {
                    console.error(`Mismatched braces: ${openBraces} opening vs ${closeBraces} closing`);
                    
                    // Create a notification for the user
                    createJavaScriptBlockNotification(script, `Mismatched braces: ${openBraces} opening vs ${closeBraces} closing`);
                    
                    // Try to fix the script
                    fixJavaScriptBlock(script, openBraces, closeBraces);
                }
            }
        });
    }
    
    // Function to create a notification for unclosed tags
    function createUnclosedTagsNotification(unclosedTags) {
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.top = '10px';
        notification.style.right = '10px';
        notification.style.backgroundColor = '#f44336';
        notification.style.color = 'white';
        notification.style.padding = '10px';
        notification.style.borderRadius = '5px';
        notification.style.zIndex = '9999';
        notification.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.2)';
        notification.style.maxWidth = '400px';
        
        const title = document.createElement('h3');
        title.textContent = 'Unclosed HTML Tags Detected';
        title.style.margin = '0 0 10px 0';
        
        notification.appendChild(title);
        
        const list = document.createElement('ul');
        list.style.margin = '0';
        list.style.padding = '0 0 0 20px';
        
        unclosedTags.forEach(tag => {
            const item = document.createElement('li');
            item.textContent = `${tag.tagName} (${tag.type})`;
            list.appendChild(item);
        });
        
        notification.appendChild(list);
        
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close';
        closeButton.style.marginTop = '10px';
        closeButton.style.padding = '5px 10px';
        closeButton.style.backgroundColor = 'white';
        closeButton.style.color = '#f44336';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '3px';
        closeButton.style.cursor = 'pointer';
        
        closeButton.onclick = function() {
            document.body.removeChild(notification);
        };
        
        notification.appendChild(closeButton);
        
        document.body.appendChild(notification);
    }
    
    // Function to create a notification for unclosed script tags
    function createUnclosedScriptsNotification(unclosedScripts) {
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.top = '10px';
        notification.style.right = '10px';
        notification.style.backgroundColor = '#ff9800';
        notification.style.color = 'white';
        notification.style.padding = '10px';
        notification.style.borderRadius = '5px';
        notification.style.zIndex = '9999';
        notification.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.2)';
        notification.style.maxWidth = '400px';
        
        const title = document.createElement('h3');
        title.textContent = 'Unclosed Script Tags Detected';
        title.style.margin = '0 0 10px 0';
        
        notification.appendChild(title);
        
        const message = document.createElement('p');
        message.textContent = `Found ${unclosedScripts.length} unclosed script tags.`;
        message.style.margin = '0 0 10px 0';
        
        notification.appendChild(message);
        
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close';
        closeButton.style.marginTop = '10px';
        closeButton.style.padding = '5px 10px';
        closeButton.style.backgroundColor = 'white';
        closeButton.style.color = '#ff9800';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '3px';
        closeButton.style.cursor = 'pointer';
        
        closeButton.onclick = function() {
            document.body.removeChild(notification);
        };
        
        notification.appendChild(closeButton);
        
        document.body.appendChild(notification);
    }
    
    // Function to create a notification for JavaScript block issues
    function createJavaScriptBlockNotification(script, message) {
        const notification = document.createElement('div');
        notification.style.position = 'fixed';
        notification.style.top = '10px';
        notification.style.right = '10px';
        notification.style.backgroundColor = '#2196F3';
        notification.style.color = 'white';
        notification.style.padding = '10px';
        notification.style.borderRadius = '5px';
        notification.style.zIndex = '9999';
        notification.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.2)';
        notification.style.maxWidth = '400px';
        
        const title = document.createElement('h3');
        title.textContent = 'JavaScript Syntax Issue Detected';
        title.style.margin = '0 0 10px 0';
        
        notification.appendChild(title);
        
        const messageElement = document.createElement('p');
        messageElement.textContent = message;
        messageElement.style.margin = '0 0 10px 0';
        
        notification.appendChild(messageElement);
        
        const closeButton = document.createElement('button');
        closeButton.textContent = 'Close';
        closeButton.style.marginTop = '10px';
        closeButton.style.padding = '5px 10px';
        closeButton.style.backgroundColor = 'white';
        closeButton.style.color = '#2196F3';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '3px';
        closeButton.style.cursor = 'pointer';
        
        closeButton.onclick = function() {
            document.body.removeChild(notification);
        };
        
        notification.appendChild(closeButton);
        
        document.body.appendChild(notification);
    }
    
    // Function to fix unclosed tags
    function fixUnclosedTags(unclosedTags) {
        // This is a client-side fix that won't persist
        // It's just to prevent errors in the current page view
        unclosedTags.forEach(tag => {
            if (tag.type === 'unclosed') {
                console.log(`Adding missing closing tag for ${tag.tagName}`);
                
                // Create a closing tag
                const closingTag = document.createComment(`Auto-added closing tag for ${tag.tagName}`);
                document.body.appendChild(closingTag);
                
                const actualClosingTag = document.createElement(tag.tagName);
                actualClosingTag.style.display = 'none';
                document.body.appendChild(actualClosingTag);
            }
        });
    }
    
    // Function to fix unclosed script tags
    function fixUnclosedScriptTags(unclosedScripts) {
        // This is a client-side fix that won't persist
        // It's just to prevent errors in the current page view
        unclosedScripts.forEach(script => {
            console.log(`Adding missing closing tag for script at position ${script.position}`);
            
            // Create a closing script tag
            const closingTag = document.createComment('Auto-added closing script tag');
            document.body.appendChild(closingTag);
            
            const actualClosingScript = document.createElement('script');
            actualClosingScript.type = 'text/javascript';
            actualClosingScript.textContent = '/* Auto-added closing script */';
            document.body.appendChild(actualClosingScript);
        });
    }
    
    // Function to fix JavaScript blocks with syntax errors
    function fixJavaScriptBlock(script, openBraces, closeBraces) {
        // This is a client-side fix that won't persist
        // It's just to prevent errors in the current page view
        if (openBraces > closeBraces) {
            const missingBraces = openBraces - closeBraces;
            console.log(`Adding ${missingBraces} missing closing braces to script`);
            
            // Create a new script with the fixed content
            const fixedScript = document.createElement('script');
            fixedScript.type = script.type || 'text/javascript';
            fixedScript.textContent = script.textContent + '\n' + '}'.repeat(missingBraces) + '\n/* Auto-fixed script */';
            
            // Replace the original script
            script.parentNode.replaceChild(fixedScript, script);
        }
    }
    
    // Run the HTML validator when the page is loaded
    window.addEventListener('load', validateHtmlStructure);
})();
