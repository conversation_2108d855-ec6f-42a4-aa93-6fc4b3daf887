#!/bin/bash

# run_app.sh - <PERSON><PERSON><PERSON> to run the DavarTruth application with the correct Python environment
# This script ensures that the application runs with the virtual environment's Python
# which has all the required dependencies installed.

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}DavarTruth Application Runner${NC}"
echo "==============================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${RED}Error: Virtual environment not found!${NC}"
    echo "Creating a new virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to create virtual environment!${NC}"
        exit 1
    fi
    echo "Installing requirements..."
    ./venv/bin/pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to install requirements. Please check your requirements.txt file.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Virtual environment created successfully.${NC}"
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate
if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to activate virtual environment.${NC}"
    exit 1
fi

# Check if requirements are installed
echo "Checking dependencies..."
if ! python -c "import fastapi" &> /dev/null; then
    echo -e "${YELLOW}FastAPI not found. Installing dependencies...${NC}"
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to install dependencies. Please check your requirements.txt file.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Dependencies installed successfully.${NC}"
else
    echo -e "${GREEN}FastAPI is already installed.${NC}"
fi

# Start the FastAPI application with uvicorn
echo -e "${GREEN}Starting FastAPI application...${NC}"
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Deactivate virtual environment when done (this will only run if uvicorn is stopped)
deactivate
