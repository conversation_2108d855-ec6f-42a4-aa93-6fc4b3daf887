{% extends "base.html" %}

{% block title %}Login to DavarTruth{% endblock %}

{% block content %}
<div class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md border-t-4 border-primary-custom">
    <h2 class="text-2xl font-bold mb-6 text-center">Login</h2>

    {% if error %}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        {{ error }}
    </div>
    {% endif %}

    {% if success %}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        {{ success }}
    </div>
    {% endif %}

    <form method="POST" action="/login">
        {% if next %}
        <input type="hidden" name="next" value="{{ next }}">
        {% endif %}
        <div class="mb-4">
            <label for="username" class="block text-gray-700 text-sm font-bold mb-2"><PERSON><PERSON><PERSON> or Email or Phone</label>
            <input
                type="text"
                id="username"
                name="username"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
                placeholder="Enter username, email, or phone number"
            >
        </div>

        <div class="mb-6">
            <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password</label>
            <div class="relative">
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    required
                >
                <button
                    type="button"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                    onclick="togglePasswordVisibility('password', 'eye-icon', 'eye-off-icon')"
                >
                    <svg id="eye-icon" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <svg id="eye-off-icon" class="h-5 w-5 text-gray-500 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                    </svg>
                </button>
            </div>
        </div>

        <div class="flex items-center mb-4">
            <input type="checkbox" id="remember_me" name="remember_me" class="mr-2">
            <label for="remember_me" class="text-sm text-gray-700">Remember me for 30 days</label>
        </div>

        <div class="flex items-center justify-between mb-4">
            <button
                type="submit"
                class="bg-primary-custom hover:bg-primary-dark text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
                Sign In
            </button>
            <a href="/register" class="inline-block align-baseline font-bold text-sm text-primary-custom hover:text-primary-dark">
                Don't have an account?
            </a>
        </div>

        <!-- WebAuthn (Passkey) Login Button -->
        <div class="webauthn-support hidden mb-4">
            <button
                type="button"
                class="webauthn-login w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center justify-center"
            >
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
                </svg>
                Sign in with Passkey
            </button>
        </div>

        <!-- Google Login Button -->
        <div class="mb-4">
            <a href="/auth/login/google" class="w-full bg-white border border-gray-300 hover:bg-gray-50 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center justify-center">
                <svg class="h-5 w-5 mr-2" viewBox="0 0 24 24">
                    <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z" fill="#4285F4"/>
                    <path d="M7.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C12.503,2.988,10.139,2,7.545,2C2.021,2,-2.457,6.477,-2.457,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L7.545,10.239z" fill="#34A853"/>
                    <path d="M2.545,7.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C7.503,2.988,5.139,2,2.545,2C-2.979,2,-7.457,6.477,-7.457,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L2.545,7.239z" fill="#FBBC05"/>
                    <path d="M2.545,16.761v-3.821h5.445c-0.712-2.315-2.647-3.972-5.445-3.972c-3.332,0-6.033,2.701-6.033,6.032s2.701,6.032,6.033,6.032c1.498,0,2.866-0.549,3.921-1.453l2.814,2.814C7.503,21.012,5.139,22,2.545,22C-2.979,22,-7.457,17.523,-7.457,12s4.478-10,10.002-10c8.396,0,10.249,7.85,9.426,11.748L2.545,16.761z" fill="#EA4335"/>
                </svg>
                Sign in with Google
            </a>
            <!-- Test link for debugging -->
            <div class="mt-2 text-center">
                <a href="/auth/google-test" class="text-xs text-gray-500 hover:text-gray-700">Test Google Auth</a>
            </div>
        </div>

        <div class="text-center">
            <a href="/forgot-password" class="inline-block align-baseline font-bold text-sm text-gray-600 hover:text-gray-800">
                Forgot Password?
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
function togglePasswordVisibility(inputId, eyeIconId, eyeOffIconId) {
    const passwordInput = document.getElementById(inputId);
    const eyeIcon = document.getElementById(eyeIconId);
    const eyeOffIcon = document.getElementById(eyeOffIconId);

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.classList.add('hidden');
        eyeOffIcon.classList.remove('hidden');
    } else {
        passwordInput.type = 'password';
        eyeIcon.classList.remove('hidden');
        eyeOffIcon.classList.add('hidden');
    }
}
</script>
{% endblock %}
