<!DOCTYPE html>
<html lang="{{ request.cookies.get('lang', 'en') }}">
<head>
    <!-- Removed inline script that was causing layout shifts -->

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#5c0e14">
    <meta name="description" content="DavarTruth: Living in it, Living on it - A platform for sharing biblical truth and connecting with others in faith">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    {% if user %}
    <meta name="user-id" content="{{ user.id }}">
    {% endif %}
    <title>{% block title %}DavarTruth{% endblock %}</title>

    <!-- PWA manifest -->
    <link rel="manifest" href="/static/manifest.json">

    <!-- Favicon -->
    <link rel="icon" href="/static/img/logos/favicon-32x32.png">
    <link rel="shortcut icon" href="/static/img/logos/favicon-32x32.png">
    <link rel="icon" href="/static/img/logos/favicon-32x32.png" type="image/png">
    <link rel="shortcut icon" href="/static/img/logos/favicon-32x32.png" type="image/png">

    <!-- Apple touch icons -->
    <link rel="apple-touch-icon" href="/static/img/logos/logo-150px-optimized.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/static/img/logos/logo-150px-optimized.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/img/logos/logo-150px-optimized.png">
    <link rel="apple-touch-icon" sizes="167x167" href="/static/img/logos/logo-150px-optimized.png">

    <!-- Additional favicon sizes -->
    <link rel="icon" type="image/png" sizes="32x32" href="/static/img/logos/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/img/logos/favicon-16x16.png">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://unpkg.com" crossorigin>

    <!-- Preload critical resources -->
    <link rel="preload" href="{{ url_for('static', path='/css/main.css') }}" as="style">
    <link rel="preload" href="{{ url_for('static', path='/css/theme.css') }}" as="style">
    <link rel="preload" href="{{ url_for('static', path='/js/app.js') }}" as="script">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" as="style">

    <!-- Critical CSS inlined for faster rendering -->
    <style>
        /* Critical CSS for DavarTruth - Only styles needed for above-the-fold content */

        /* Base styles */
        :root {
          --primary-custom: #5c0e14;
          --primary-dark: #4a0b10;
          --secondary-custom: #f0e193;
          --secondary-dark: #e6d67f;
        }

        body {
          margin: 0;
          padding: 0;
          font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
          background-color: #FCF8D6;
          line-height: 1.5;
        }

        /* Container */
        .container {
          width: 100%;
          max-width: 1024px;
          margin-left: auto;
          margin-right: auto;
          padding-left: 1rem;
          padding-right: 1rem;
        }

        /* Navbar */
        .bg-primary-custom {
          background-color: var(--primary-custom);
        }

        .text-white {
          color: white;
        }

        .text-secondary-custom {
          color: var(--secondary-custom);
        }

        .rounded-lg {
          border-radius: 0.5rem;
        }

        .shadow-md {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .py-6 {
          padding-top: 1.5rem;
          padding-bottom: 1.5rem;
        }

        .mb-6 {
          margin-bottom: 1.5rem;
        }

        .p-4 {
          padding: 1rem;
        }

        .flex {
          display: flex;
        }

        .flex-col {
          flex-direction: column;
        }

        .items-center {
          align-items: center;
        }

        .justify-between {
          justify-content: space-between;
        }

        .w-full {
          width: 100%;
        }

        .mb-4 {
          margin-bottom: 1rem;
        }

        .text-4xl {
          font-size: 2.25rem;
        }

        .font-bold {
          font-weight: 700;
        }

        .tracking-tight {
          letter-spacing: -0.025em;
        }

        /* Offline indicator */
        #offline-indicator {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background-color: var(--primary-custom);
          color: white;
          padding: 0.5rem;
          text-align: center;
          font-weight: bold;
          z-index: 50;
        }

        .hidden {
          display: none;
        }

        /* Media queries for responsive design */
        @media (min-width: 1024px) {
          .lg\:flex-row {
            flex-direction: row;
          }

          .lg\:w-auto {
            width: auto;
          }

          .lg\:mb-0 {
            margin-bottom: 0;
          }

          .lg\:hidden {
            display: none;
          }

          .lg\:flex {
            display: flex;
          }

          .lg\:space-x-4 > * + * {
            margin-left: 1rem;
          }

          .lg\:space-y-0 > * + * {
            margin-top: 0;
          }
        }
    </style>

    <!-- Critical CSS loaded directly -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="{{ url_for('static', path='/css/main.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', path='/css/theme.css') }}" rel="stylesheet">

    <!-- Non-critical CSS loaded asynchronously -->
    <link href="{{ url_for('static', path='/css/flip.css') }}" rel="stylesheet" media="print" onload="this.media='all'">
    <link href="{{ url_for('static', path='/css/navbar.css') }}" rel="stylesheet" media="print" onload="this.media='all'">
    <link href="{{ url_for('static', path='/css/aspect-ratio-fix.css') }}" rel="stylesheet" media="print" onload="this.media='all'">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet" media="print" onload="this.media='all'">

    <!-- Fallback for browsers that don't support onload -->
    <noscript>
        <link href="{{ url_for('static', path='/css/flip.css') }}" rel="stylesheet">
        <link href="{{ url_for('static', path='/css/navbar.css') }}" rel="stylesheet">
        <link href="{{ url_for('static', path='/css/aspect-ratio-fix.css') }}" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    </noscript>

    <!-- Dropdown menu transitions and accessibility styles -->
    <style>
        .transition {
            transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
            transition-duration: 150ms;
        }
        .transition-fastest {
            transition-duration: 75ms;
        }
        .transition-faster {
            transition-duration: 100ms;
        }
        .transition-fast {
            transition-duration: 150ms;
        }

        /* Accessibility toggle switch */
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: var(--primary-custom);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-custom);
        }

        input:checked + .slider:before {
            transform: translateX(16px);
        }

        .slider.round {
            border-radius: 24px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        /* High contrast mode */
        body.high-contrast {
            background-color: #000 !important;
            color: #fff !important;
        }

        body.high-contrast a,
        body.high-contrast button,
        body.high-contrast h1,
        body.high-contrast h2,
        body.high-contrast h3,
        body.high-contrast h4,
        body.high-contrast h5,
        body.high-contrast h6 {
            color: #ffff00 !important;
        }

        body.high-contrast .bg-white,
        body.high-contrast .bg-gray-50,
        body.high-contrast .bg-gray-100,
        body.high-contrast .bg-gray-200 {
            background-color: #000 !important;
        }

        body.high-contrast .border,
        body.high-contrast .border-gray-200,
        body.high-contrast .border-gray-300 {
            border-color: #fff !important;
        }

        /* Dyslexic font */
        body.dyslexic-font {
            font-family: 'OpenDyslexic', sans-serif !important;
        }

        /* Font size adjustments */
        body.font-size-larger {
            font-size: 120% !important;
        }

        body.font-size-largest {
            font-size: 140% !important;
        }
        .transition-medium {
            transition-duration: 200ms;
        }
        .ease-in {
            transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
        }
        .ease-out {
            transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
        }
        .transform {
            --transform-translate-x: 0;
            --transform-translate-y: 0;
            --transform-rotate: 0;
            --transform-skew-x: 0;
            --transform-skew-y: 0;
            --transform-scale-x: 1;
            --transform-scale-y: 1;
            transform: translateX(var(--transform-translate-x)) translateY(var(--transform-translate-y)) rotate(var(--transform-rotate)) skewX(var(--transform-skew-x)) skewY(var(--transform-skew-y)) scaleX(var(--transform-scale-x)) scaleY(var(--transform-scale-y));
        }
        .scale-95 {
            --transform-scale-x: .95;
            --transform-scale-y: .95;
        }
        .scale-100 {
            --transform-scale-x: 1;
            --transform-scale-y: 1;
        }
        .rotate-180 {
            --transform-rotate: 180deg;
        }
    </style>

    <!-- Bug Report Modal -->
    <div id="bug-report-modal" class="fixed inset-0 z-50 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-bug text-red-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Report a Bug
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500 mb-4">
                                    Please describe the issue you're experiencing. This will help us improve the platform.
                                </p>

                                <form id="bug-report-form">
                                    <!-- Bug type selection -->
                                    <div class="mb-4">
                                        <label for="bug-type" class="block text-sm font-medium text-gray-700 mb-1">Type of Issue</label>
                                        <select
                                            id="bug-type"
                                            name="bug_type"
                                            autocomplete="off"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-custom focus:ring focus:ring-primary-custom focus:ring-opacity-50"
                                            required
                                        >
                                            <option value="">Select an issue type</option>
                                            <option value="functionality">Functionality Issue</option>
                                            <option value="display">Display/Visual Issue</option>
                                            <option value="performance">Performance Issue</option>
                                            <option value="content">Content Issue</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>

                                    <!-- Bug description -->
                                    <div class="mb-4">
                                        <label for="bug-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                        <textarea
                                            id="bug-description"
                                            name="description"
                                            rows="4"
                                            autocomplete="off"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-custom focus:ring focus:ring-primary-custom focus:ring-opacity-50"
                                            placeholder="Please describe what happened and what you expected to happen..."
                                            required
                                        ></textarea>
                                    </div>

                                    <!-- Steps to reproduce -->
                                    <div class="mb-4">
                                        <label for="bug-steps" class="block text-sm font-medium text-gray-700 mb-1">Steps to Reproduce</label>
                                        <textarea
                                            id="bug-steps"
                                            name="steps"
                                            rows="3"
                                            autocomplete="off"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-custom focus:ring focus:ring-primary-custom focus:ring-opacity-50"
                                            placeholder="1. Go to...
2. Click on...
3. Observe that..."
                                        ></textarea>
                                    </div>

                                    <!-- Email (optional) -->
                                    <div class="mb-4">
                                        <label for="bug-email" class="block text-sm font-medium text-gray-700 mb-1">Your Email (optional)</label>
                                        <input
                                            type="email"
                                            id="bug-email"
                                            name="email"
                                            autocomplete="email"
                                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-custom focus:ring focus:ring-primary-custom focus:ring-opacity-50"
                                            placeholder="<EMAIL>"
                                        >
                                        <p class="text-xs text-gray-500 mt-1">We'll only use this to follow up on your report if needed.</p>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                        type="button"
                        id="submit-bug-report"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-custom text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-custom sm:ml-3 sm:w-auto sm:text-sm"
                    >
                        Submit Report
                    </button>
                    <button
                        type="button"
                        id="cancel-bug-report"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-custom sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div id="bug-success-modal" class="fixed inset-0 z-50 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Bug Report Submitted
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Thank you for helping us improve! Your report has been submitted successfully.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                        type="button"
                        id="close-success-modal"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-custom text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-custom sm:ml-3 sm:w-auto sm:text-sm"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple HTML fix script -->
    <script src="/static/js/simple-html-fix.js"></script>

    <!-- Alpine.js initialization helper - must be loaded first -->
    <script src="/static/js/alpine-init-helper.min.js"></script>

    <!-- IndexedDB helper library -->
    <script src="/static/js/index-min.js" defer></script>

    <!-- Alpine.js component functions - must be loaded before Alpine.js -->
    <script src="/static/js/alpine-component-functions.min.js"></script>

    <!-- Scripts - Optimized loading -->
    <!-- Script loader for optimized script loading -->
    <script src="/static/js/script-loader-fixed.min.js"></script>

    <!-- Alpine.js components fix script -->
    <script src="/static/js/alpine-components-fix.min.js" defer></script>

    <!-- Alpine.js components script -->
    <script src="/static/js/alpine-components-fixed.min.js" defer></script>

    <!-- Alpine.js initialization check -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Alpine.js is loaded
            if (typeof window.Alpine === 'undefined') {
                console.error('Alpine.js failed to load. Attempting to load from backup source...');

                // Create a new script element for the backup source
                const alpineScript = document.createElement('script');
                alpineScript.src = 'https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js';
                alpineScript.defer = true;

                // Add the script to the document
                document.head.appendChild(alpineScript);

                // Also load the components fix script
                const fixScript = document.createElement('script');
                fixScript.src = '/static/js/alpine-components-fix.min.js';
                fixScript.defer = true;
                document.head.appendChild(fixScript);

                // Load the components script
                const componentsScript = document.createElement('script');
                componentsScript.src = '/static/js/alpine-components-fixed.min.js';
                componentsScript.defer = true;
                document.head.appendChild(componentsScript);
            }
        });
    </script>

    <!-- Quirks mode detection and fixing scripts -->
    <script src="/static/js/quirks-mode-detector.js"></script>

    <!-- Non-critical scripts deferred -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js" defer></script>
    <script src="/static/js/idb.js" defer></script>
    <script src="/static/js/pwa.js" defer></script>

    <!-- WebAuthn (Passkey) Support - loaded on demand -->
    <script src="/static/js/vendor/simplewebauthn-browser.min.js" defer></script>
    <script src="/static/js/webauthn.js" defer></script>

    <!-- CSS loading fallback script -->
    <script>
      // Fallback for browsers that don't support preload or async CSS loading
      (function() {
        // Check if CSS files are already loaded
        function isCssLoaded(href) {
          var links = document.getElementsByTagName('link');
          for (var i = 0; i < links.length; i++) {
            if (links[i].rel === 'stylesheet' && links[i].href.indexOf(href) !== -1) {
              return true;
            }
          }
          return false;
        }

        // Load CSS file if not already loaded
        function loadCssIfNeeded(href) {
          if (!isCssLoaded(href)) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.type = 'text/css';
            document.head.appendChild(link);
          }
        }

        // Load critical CSS files
        loadCssIfNeeded('/static/css/main.css');
        loadCssIfNeeded('/static/css/theme.css');

        // Load non-critical CSS files with a slight delay
        setTimeout(function() {
          loadCssIfNeeded('/static/css/flip.css');
          loadCssIfNeeded('/static/css/navbar.css');
          loadCssIfNeeded('/static/css/aspect-ratio-fix.css');
        }, 100);
      })();
    </script>

    <!-- Image optimization script -->
    <script src="{{ url_for('static', path='/js/image-optimizer-fixed.js') }}" defer></script>

    <!-- Bug Report Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize bug report form submission
            const submitBugReportBtn = document.getElementById('submit-bug-report');
            if (submitBugReportBtn) {
                submitBugReportBtn.addEventListener('click', function() {
                    const bugType = document.getElementById('bug-type').value;
                    const description = document.getElementById('bug-description').value;
                    const steps = document.getElementById('bug-steps').value;
                    const email = document.getElementById('bug-email').value;

                    // Validate required fields
                    if (!bugType || !description) {
                        alert('Please fill in all required fields.');
                        return;
                    }

                    // Collect browser info
                    const browserInfo = {
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        screenSize: `${window.screen.width}x${window.screen.height}`,
                        viewportSize: `${window.innerWidth}x${window.innerHeight}`,
                        url: window.location.href
                    };

                    // Submit the bug report
                    fetch('/api/bug-reports', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            bug_type: bugType,
                            description: description,
                            steps_to_reproduce: steps,
                            email: email,
                            browser_info: browserInfo
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success modal
                            document.getElementById('bug-report-modal').classList.add('hidden');
                            document.getElementById('bug-success-modal').classList.remove('hidden');
                            // Reset form
                            document.getElementById('bug-report-form').reset();
                        } else {
                            alert('Error submitting bug report: ' + (data.message || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        console.error('Error submitting bug report:', error);
                        alert('Error submitting bug report. Please try again later.');
                    });
                });
            }

            // Cancel button
            const cancelBugReportBtn = document.getElementById('cancel-bug-report');
            if (cancelBugReportBtn) {
                cancelBugReportBtn.addEventListener('click', function() {
                    document.getElementById('bug-report-modal').classList.add('hidden');
                });
            }

            // Close success modal
            const closeSuccessModalBtn = document.getElementById('close-success-modal');
            if (closeSuccessModalBtn) {
                closeSuccessModalBtn.addEventListener('click', function() {
                    document.getElementById('bug-success-modal').classList.add('hidden');
                });
            }
        });
    </script>
    <!-- Main Donation Modal (Hidden by default) -->
    <div id="main-donation-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-primary-custom sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-heart text-white"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                Support Options
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500 mb-4">
                                    At Vessels, we believe in the principle that Jesus taught: "Freely you have received, freely give" (Matthew 10:8). The Word of God is a precious gift that should be shared with all.
                                </p>
                                <p class="text-sm text-gray-500 mb-4">
                                    While we would greatly appreciate any donations to help run and maintain this blog, we want to emphasize that giving is entirely voluntary and should come from the heart.
                                </p>
                                <p class="text-sm text-gray-500">
                                    We strongly believe that God will bless this ministry and provide for our needs as necessary. Your support helps us continue our mission of sharing God's truth.
                                </p>

                                <div class="mt-4 flex flex-wrap gap-2 justify-center">
                                    <button onclick="processDonationAmount('10')" class="bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded">
                                        Donate $10
                                    </button>
                                    <button onclick="processDonationAmount('25')" class="bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded">
                                        Donate $25
                                    </button>
                                    <button onclick="processDonationAmount('50')" class="bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded">
                                        Donate $50
                                    </button>
                                    <button onclick="showCustomAmountInput()" class="bg-secondary-custom hover:bg-secondary-dark text-primary-custom px-4 py-2 rounded">
                                        Custom Amount
                                    </button>
                                </div>

                                <div id="custom-donation-input" class="mt-4 hidden">
                                    <label for="main-custom-amount" class="block text-sm font-medium text-gray-700">Enter amount:</label>
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">$</span>
                                        </div>
                                        <input
                                            type="number"
                                            name="main-custom-amount"
                                            id="main-custom-amount"
                                            autocomplete="off"
                                            class="focus:ring-primary-custom focus:border-primary-custom block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                                            placeholder="0.00"
                                            min="1"
                                            step="0.01"
                                        >
                                        <button onclick="processDonationAmount('custom')" class="mt-2 w-full bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded">
                                            Donate
                                        </button>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <p class="text-sm text-gray-500 italic text-center">
                                        "And my God will meet all your needs according to the riches of his glory in Christ Jesus." - Philippians 4:19
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="hideMainDonationModal()" class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-custom sm:mt-0 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Donation modal functions
        function showMainDonationModal() {
            document.getElementById('main-donation-modal').classList.remove('hidden');
            document.getElementById('custom-donation-input').classList.add('hidden');
        }

        function hideMainDonationModal() {
            document.getElementById('main-donation-modal').classList.add('hidden');
        }

        function showCustomAmountInput() {
            document.getElementById('custom-donation-input').classList.remove('hidden');
        }

        function processDonationAmount(amount) {
            if (amount === 'custom') {
                const customAmount = document.getElementById('main-custom-amount').value;
                if (!customAmount || isNaN(customAmount) || customAmount <= 0) {
                    alert('Please enter a valid donation amount.');
                    return;
                }
                amount = customAmount;
            }

            // In a real implementation, this would redirect to a payment processor
            alert(`Thank you for your generous donation of $${amount}! This would normally redirect to a secure payment page.`);
            hideMainDonationModal();

            // Example of what you might do in production:
            // window.location.href = `/process-payment?amount=${amount}`;
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the bug report modal
            window.reportBugModal = {
                modal: document.getElementById('bug-report-modal'),
                successModal: document.getElementById('bug-success-modal'),
                form: document.getElementById('bug-report-form'),
                submitButton: document.getElementById('submit-bug-report'),
                cancelButton: document.getElementById('cancel-bug-report'),
                closeSuccessButton: document.getElementById('close-success-modal'),

                show() {
                    this.modal.classList.remove('hidden');
                },

                hide() {
                    this.modal.classList.add('hidden');
                    this.form.reset();
                },

                showSuccess() {
                    this.hide();
                    this.successModal.classList.remove('hidden');
                },

                hideSuccess() {
                    this.successModal.classList.add('hidden');
                },

                submitReport() {
                    // Get form data
                    const bugType = document.getElementById('bug-type').value;
                    const description = document.getElementById('bug-description').value;
                    const steps = document.getElementById('bug-steps').value;
                    const email = document.getElementById('bug-email').value;

                    // Validate required fields
                    if (!bugType || !description) {
                        alert('Please fill in all required fields.');
                        return;
                    }

                    // Collect browser and system info
                    const browserInfo = {
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        screenSize: `${window.screen.width}x${window.screen.height}`,
                        viewportSize: `${window.innerWidth}x${window.innerHeight}`,
                        url: window.location.href
                    };

                    // Prepare data for submission
                    const reportData = {
                        bug_type: bugType,
                        description: description,
                        steps_to_reproduce: steps,
                        email: email,
                        browser_info: browserInfo,
                        timestamp: new Date().toISOString()
                    };

                    // Submit the report to the server
                    fetch('/api/bug-reports', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(reportData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Bug report submitted successfully:', data);
                        // Also store in localStorage as a backup
                        const reports = JSON.parse(localStorage.getItem('bugReports') || '[]');
                        reports.push(reportData);
                        localStorage.setItem('bugReports', JSON.stringify(reports));
                    })
                    .catch(error => {
                        console.error('Error submitting bug report:', error);
                        // Store in localStorage if server submission fails
                        const reports = JSON.parse(localStorage.getItem('bugReports') || '[]');
                        reports.push(reportData);
                        localStorage.setItem('bugReports', JSON.stringify(reports));
                    });

                    // Show success message
                    this.showSuccess();
                }
            };

            // Set up event listeners
            window.reportBugModal.submitButton.addEventListener('click', function() {
                window.reportBugModal.submitReport();
            });

            window.reportBugModal.cancelButton.addEventListener('click', function() {
                window.reportBugModal.hide();
            });

            window.reportBugModal.closeSuccessButton.addEventListener('click', function() {
                window.reportBugModal.hideSuccess();
            });

            // Close modals when clicking on the overlay
            window.reportBugModal.modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    window.reportBugModal.hide();
                }
            });

            window.reportBugModal.successModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    window.reportBugModal.hideSuccess();
                }
            });
        });
    </script>

    {% block head %}{% endblock %}
</head>
<body style="background-color: #FCF8D6;" class="{% if user %}user-logged-in{% else %}user-not-logged-in{% endif %}">
    <div id="offline-indicator" class="hidden fixed top-0 left-0 right-0 bg-primary-custom text-white p-2 text-center font-bold z-50">
        <div class="flex items-center justify-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            You are currently offline. Some features may be limited.
        </div>
    </div>

    <div class="container mx-auto px-4 min-h-screen" style="max-width: 1024px;">
        <header class="py-6">
            <nav class="bg-primary-custom rounded-lg shadow-md mb-6 border-b border-primary-dark" style="display: block; width: 100%; box-sizing: border-box;">
                <!-- Main navbar container -->
                <div class="flex flex-col lg:flex-row justify-between items-center p-4 relative" style="flex-wrap: nowrap; width: 100%; box-sizing: border-box;">
                    <!-- Logo -->
                    <div class="flex items-center justify-between w-full lg:w-auto mb-4 lg:mb-0">
                        <div class="flex items-center">
                            <!-- Logo Image -->
                            <img src="/static/img/logos/logo-150px-optimized.png" alt="DavarTruth Logo" class="h-12 w-auto mr-3">
                        </div>
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" class="lg:hidden text-white focus:outline-none" onclick="toggleMobileMenu()">
                            <i class="fas fa-bars text-2xl"></i>
                        </button>
                    </div>

                    <!-- Navigation links - hidden on mobile unless toggled -->
                    <div id="navbar-links" class="hidden lg:flex flex-col lg:flex-row items-center justify-between lg:flex-grow space-y-4 lg:space-y-0 lg:mx-4 w-full lg:w-auto">
                        <!-- Main navigation -->
                        <div class="flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-4">
                            <!-- App Switcher -->
                            <div class="app-switcher flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-2 mb-2 lg:mb-0">
                                {% set current_app = request.url.path.split('/')[1] %}
                                <a href="/" class="px-4 py-2 text-white {% if not current_app or current_app == '' %}bg-primary-dark font-bold{% else %}hover:text-secondary-custom hover:bg-primary-dark{% endif %} rounded-md transition duration-200">
                                    <i class="fas fa-home mr-1"></i> Blog
                                </a>
                                <a href="/chat" class="px-4 py-2 text-white {% if current_app == 'chat' %}bg-primary-dark font-bold{% else %}hover:text-secondary-custom hover:bg-primary-dark{% endif %} rounded-md transition duration-200">
                                    <i class="fab fa-whatsapp mr-1"></i> Chat
                                </a>
                            </div>
                            <a href="/bible-study" class="px-4 py-2 text-white hover:text-secondary-custom hover:bg-primary-dark rounded-md transition duration-200">
                                <i class="fas fa-bible mr-1"></i> Bible Study
                            </a>
                        </div>

                        <!-- Secondary navigation -->
                        <div class="flex flex-col lg:flex-row items-center space-y-2 lg:space-y-0 lg:space-x-4 lg:ml-4">
                            <!-- Language Selector -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="flex items-center px-3 py-2 text-white hover:text-secondary-custom hover:bg-primary-dark rounded-md focus:outline-none transition duration-200">
                                    <i class="fas fa-globe mr-1"></i>
                                    <span class="mx-1">{{ request.cookies.get('lang', 'en')|upper }}</span>
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </button>
                                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50" style="display: none;">
                                    <a href="#" onclick="setLanguage('en'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">English</a>
                                    <a href="#" onclick="setLanguage('es'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">Español</a>
                                    <a href="#" onclick="setLanguage('fr'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">Français</a>
                                    <a href="#" onclick="setLanguage('de'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">Deutsch</a>
                                    <a href="#" onclick="setLanguage('zh'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">中文</a>
                                    <a href="#" onclick="setLanguage('ar'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">العربية</a>
                                    <a href="#" onclick="setLanguage('hi'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">हिन्दी</a>
                                    <a href="#" onclick="setLanguage('pt'); return false;" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">Português</a>
                                </div>
                            </div>

                            <!-- Accessibility Menu -->
                            <div class="relative accessibility-menu lg:block" x-data="{ open: false }">
                                <button @click="open = !open" class="flex items-center px-3 py-2 text-white hover:text-secondary-custom hover:bg-primary-dark rounded-md focus:outline-none transition duration-200">
                                    <i class="fas fa-universal-access mr-1"></i>
                                    <span class="mx-1">Accessibility</span>
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </button>
                                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 z-50" style="display: none;">
                            <div class="px-4 py-2">
                                <div class="flex items-center justify-between mb-2">
                                    <label for="font-size" class="block text-sm text-gray-700">Font Size</label>
                                    <div class="flex items-center">
                                        <button onclick="changeFontSize('decrease')" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded-l text-sm transition duration-150">A-</button>
                                        <button onclick="changeFontSize('reset')" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 text-sm transition duration-150">Reset</button>
                                        <button onclick="changeFontSize('increase')" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded-r text-sm transition duration-150">A+</button>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between mb-2">
                                    <label for="contrast" class="block text-sm text-gray-700">High Contrast</label>
                                    <label class="switch">
                                        <input type="checkbox" id="contrast-toggle" onchange="toggleHighContrast()">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between mb-2">
                                    <label for="dyslexic-font" class="block text-sm text-gray-700">Dyslexic Font</label>
                                    <label class="switch">
                                        <input type="checkbox" id="dyslexic-font-toggle" onchange="toggleDyslexicFont()">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <label for="screen-reader" class="block text-sm text-gray-700">Screen Reader</label>
                                    <button onclick="toggleScreenReader()" class="px-2 py-1 bg-primary-custom text-white rounded text-sm hover:bg-primary-dark transition duration-150 shadow-sm">Enable</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if user %}
                        <!-- User dropdown menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center px-3 py-2 text-white hover:text-secondary-custom hover:bg-primary-dark rounded-md focus:outline-none transition duration-200">
                                <!-- User avatar -->
                                <div class="w-8 h-8 rounded-full bg-secondary-custom text-primary-custom flex items-center justify-center mr-2 overflow-hidden">
                                    {% if user.profile_picture %}
                                        <img src="{{ user.profile_picture }}" alt="{{ user.username }}" class="w-full h-full object-cover">
                                    {% else %}
                                        {{ user.username[0].upper() }}
                                    {% endif %}
                                </div>
                                <span>{{ user.username }}</span>
                                <svg class="w-4 h-4 ml-1" :class="{'transform rotate-180': open}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <!-- Dropdown menu -->
                            <div
                                x-show="open"
                                @click.away="open = false"
                                x-transition:enter="transition ease-out duration-100"
                                x-transition:enter-start="transform opacity-0 scale-95"
                                x-transition:enter-end="transform opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-75"
                                x-transition:leave-start="transform opacity-100 scale-100"
                                x-transition:leave-end="transform opacity-0 scale-95"
                                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"
                            >
                                <a href="/author-dashboard" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">
                                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                                </a>
                                <a href="/create-post" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">
                                    <i class="fas fa-pen mr-2"></i> Create Post
                                </a>
                                <a href="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition duration-150">
                                    <i class="fas fa-user mr-2"></i> Profile
                                </a>
                                <div class="border-t border-gray-100 my-1"></div>
                                <a href="/logout" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition duration-150">
                                    <i class="fas fa-sign-out-alt mr-2"></i> Logout
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <div class="flex mt-4 lg:mt-0 lg:ml-4 navbar-auth" style="flex-shrink: 0; position: relative; z-index: 10;">
                            <a href="/login" class="mr-2 px-4 py-2 bg-secondary-custom text-primary-custom rounded-md hover:bg-secondary-dark font-medium transition duration-200 shadow-sm" style="white-space: nowrap;">Login</a>
                            <a href="/register" class="px-4 py-2 bg-white text-primary-custom rounded-md hover:bg-gray-100 font-medium border border-gray-200 transition duration-200 shadow-sm" style="white-space: nowrap;">Register</a>
                        </div>
                    {% endif %}
                </div>
            </nav>
        </header>
        <main class="relative">
            {% block content %}{% endblock %}
        </main>

        {% block footer %}{% endblock %}

    {% block scripts %}{% endblock %}

    <script>
        // Handle offline indicator
        function updateOfflineStatus() {
            const offlineIndicator = document.getElementById('offline-indicator');
            if (!navigator.onLine) {
                offlineIndicator.classList.remove('hidden');
            } else {
                offlineIndicator.classList.add('hidden');
            }
        }

        // Initial check
        updateOfflineStatus();

        // Listen for online/offline events
        window.addEventListener('online', updateOfflineStatus);
        window.addEventListener('offline', updateOfflineStatus);

        // Language selection
        function setLanguage(lang) {
            document.cookie = `lang=${lang};path=/;max-age=31536000`; // 1 year

            // Load Google Translate if changing to non-English language
            if (lang !== 'en') {
                loadGoogleTranslate();
                // Hide the load translator button
                const loadBtn = document.getElementById('load-translate-btn');
                if (loadBtn) loadBtn.style.display = 'none';
            }

            // Reload after a short delay to allow the cookie to be set
            setTimeout(() => location.reload(), 100);
        }

        // Check if OpenDyslexic font is loaded
        function loadDyslexicFont() {
            const fontLink = document.createElement('link');
            fontLink.href = 'https://cdn.jsdelivr.net/npm/font-awesome-animation@1.1.1/css/font-awesome-animation.min.css';
            fontLink.rel = 'stylesheet';
            document.head.appendChild(fontLink);

            const dyslexicFont = document.createElement('link');
            dyslexicFont.href = 'https://cdn.jsdelivr.net/npm/opendyslexic@1.0.3/open-dyslexic.min.css';
            dyslexicFont.rel = 'stylesheet';
            document.head.appendChild(dyslexicFont);
        }

        // Accessibility functions
        function changeFontSize(action) {
            const body = document.body;

            if (action === 'increase') {
                if (body.classList.contains('font-size-larger')) {
                    body.classList.remove('font-size-larger');
                    body.classList.add('font-size-largest');
                } else if (body.classList.contains('font-size-largest')) {
                    // Already at maximum size
                    return;
                } else {
                    body.classList.add('font-size-larger');
                }
            } else if (action === 'decrease') {
                if (body.classList.contains('font-size-largest')) {
                    body.classList.remove('font-size-largest');
                    body.classList.add('font-size-larger');
                } else if (body.classList.contains('font-size-larger')) {
                    body.classList.remove('font-size-larger');
                } else {
                    // Already at minimum size
                    return;
                }
            } else if (action === 'reset') {
                body.classList.remove('font-size-larger', 'font-size-largest');
            }

            // Save preference
            localStorage.setItem('font-size', body.className);
        }

        function toggleHighContrast() {
            const body = document.body;
            const contrastToggle = document.getElementById('contrast-toggle');

            if (contrastToggle.checked) {
                body.classList.add('high-contrast');
                localStorage.setItem('high-contrast', 'enabled');
            } else {
                body.classList.remove('high-contrast');
                localStorage.setItem('high-contrast', 'disabled');
            }
        }

        function toggleDyslexicFont() {
            const body = document.body;
            const dyslexicFontToggle = document.getElementById('dyslexic-font-toggle');

            if (dyslexicFontToggle.checked) {
                loadDyslexicFont();
                body.classList.add('dyslexic-font');
                localStorage.setItem('dyslexic-font', 'enabled');
            } else {
                body.classList.remove('dyslexic-font');
                localStorage.setItem('dyslexic-font', 'disabled');
            }
        }

        function toggleScreenReader() {
            alert('Screen reader functionality would be enabled here. This would typically integrate with the browser\'s accessibility features or a third-party screen reader API.');
        }

        // Toggle mobile menu
        function toggleMobileMenu() {
            const navbarLinks = document.getElementById('navbar-links');
            navbarLinks.classList.toggle('hidden');

            // Ensure proper display when toggled
            if (!navbarLinks.classList.contains('hidden')) {
                navbarLinks.style.display = 'flex';
                navbarLinks.style.flexDirection = 'column';
                navbarLinks.style.width = '100%';
            } else {
                navbarLinks.style.display = '';
            }
        }

        // Load saved accessibility preferences
        document.addEventListener('DOMContentLoaded', function() {
            // Load font size preference
            const savedFontSize = localStorage.getItem('font-size');
            if (savedFontSize) {
                if (savedFontSize.includes('font-size-larger')) {
                    document.body.classList.add('font-size-larger');
                } else if (savedFontSize.includes('font-size-largest')) {
                    document.body.classList.add('font-size-largest');
                }
            }

            // Load high contrast preference
            const savedContrast = localStorage.getItem('high-contrast');
            if (savedContrast === 'enabled') {
                document.body.classList.add('high-contrast');
                const contrastToggle = document.getElementById('contrast-toggle');
                if (contrastToggle) contrastToggle.checked = true;
            }

            // Load dyslexic font preference
            const savedDyslexicFont = localStorage.getItem('dyslexic-font');
            if (savedDyslexicFont === 'enabled') {
                loadDyslexicFont();
                document.body.classList.add('dyslexic-font');
                const dyslexicFontToggle = document.getElementById('dyslexic-font-toggle');
                if (dyslexicFontToggle) dyslexicFontToggle.checked = true;
            }
        });
    </script>

    <!-- Google Translate Integration - On-Demand Loading -->
    <script type="text/javascript">
        // Function to initialize Google Translate
        function googleTranslateElementInit() {
            new google.translate.TranslateElement({
                pageLanguage: '{{ request.cookies.get("lang", "en") }}',
                autoDisplay: false,
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE
            }, 'google_translate_element');
        }

        // Function to load Google Translate on demand
        function loadGoogleTranslate() {
            // Check if already loaded
            if (window.google && window.google.translate) {
                return;
            }

            // Create and append the script
            const translateScript = document.createElement('script');
            translateScript.type = 'text/javascript';
            translateScript.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
            translateScript.onerror = function() {
                console.log('Google Translate failed to load. It may be blocked by an ad blocker.');
                document.getElementById('google_translate_element').innerHTML =
                    '<div class="text-sm text-gray-500">Translation unavailable. Ad blocker may be active.</div>';
            };
            document.body.appendChild(translateScript);
        }
    </script>

    <!-- PWA Scripts -->
    <script src="/static/js/idb.js"></script>
    <script src="/static/js/pwa.js"></script>

    <!-- Removed redundant closing tags that were causing layout shifts -->
</body>
</html>