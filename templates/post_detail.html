{% extends "base.html" %}

{% block title %}{{ post.title }} - DavarTruth Blog{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="/static/css/blog.css">
<link rel="stylesheet" href="/static/css/z-index-fix.css">
<link rel="stylesheet" href="/static/css/navbar.css">
<link rel="stylesheet" href="/static/css/blog-components-fixed.css">

<!-- SEO Meta Tags -->
<meta name="description" content="{{ post.title }} - DavarTruth Blog">
<meta name="keywords" content="Christian blog, faith, Bible study, prayer, spiritual growth, Christianity, Jesus{% if post.category %}, {{ post.category }}{% endif %}">
<meta name="author" content="{% if post.author %}{{ post.author.username }}{% else %}DavarTruth Community{% endif %}">

<!-- Open Graph / Social Media Meta Tags -->
<meta property="og:title" content="{{ post.title }} - DavarTruth Blog">
<meta property="og:description" content="{{ post.content|striptags|truncate(150) }}">
<meta property="og:type" content="article">
<meta property="og:url" content="{{ request.url if request.url is defined else '' }}">
<meta property="og:image" content="/static/images/og-image.jpg">

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ post.title }} - DavarTruth Blog">
<meta name="twitter:description" content="{{ post.content|striptags|truncate(150) }}">
<meta name="twitter:image" content="/static/images/og-image.jpg">

<!-- Canonical URL -->
<link rel="canonical" href="{{ request.url if request.url is defined else '' }}">

<!-- Responsive Meta Tag -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<style>
    [x-cloak] { display: none !important; }

    /* Blog post content styling */
    .post-content h1 { font-size: 1.8rem; font-weight: bold; margin: 1rem 0; }
    .post-content h2 { font-size: 1.5rem; font-weight: bold; margin: 1rem 0; }
    .post-content h3 { font-size: 1.3rem; font-weight: bold; margin: 0.8rem 0; }
    .post-content h4 { font-size: 1.2rem; font-weight: bold; margin: 0.6rem 0; }
    .post-content h5 { font-size: 1.1rem; font-weight: bold; margin: 0.5rem 0; }
    .post-content h6 { font-size: 1rem; font-weight: bold; margin: 0.5rem 0; }
    .post-content p { margin-bottom: 1rem; line-height: 1.6; }
    .post-content ul, .post-content ol { margin-left: 1.5rem; margin-bottom: 1rem; }
    .post-content li { margin-bottom: 0.5rem; }
    .post-content blockquote {
        border-left: 4px solid var(--primary-custom);
        padding-left: 1rem;
        margin-left: 0;
        margin-right: 0;
        margin-bottom: 1rem;
        font-style: italic;
        color: #4b5563;
    }
    .post-content img {
        max-width: 100%;
        height: auto;
        margin: 1rem auto;
        border-radius: 0.375rem;
    }
    .post-content a {
        color: var(--primary-custom);
        text-decoration: underline;
    }
    .post-content a:hover {
        color: var(--primary-dark);
    }

    body {
        background-color: #FCF8D6;
    }

    .post-container {
        max-width: 768px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .post-header {
        margin-bottom: 2rem;
    }

    .post-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--primary-custom);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .post-meta {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        color: #6b7280;
    }

    .post-author-avatar {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 9999px;
        margin-right: 0.75rem;
        background-color: var(--primary-custom);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .post-author-name {
        font-weight: 600;
        margin-right: 0.5rem;
    }

    .post-date {
        font-size: 0.875rem;
    }

    .post-category {
        display: inline-block;
        background-color: #f3f4f6;
        color: #4b5563;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        margin-left: 1rem;
    }

    .post-body {
        background-color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .post-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }

    .post-action-button {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        background-color: #f3f4f6;
        color: #4b5563;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
    }

    .post-action-button:hover {
        background-color: #e5e7eb;
    }

    .post-action-button i {
        margin-right: 0.5rem;
    }

    .back-to-blog {
        display: inline-flex;
        align-items: center;
        color: var(--primary-custom);
        font-weight: 500;
        margin-bottom: 1rem;
    }

    .back-to-blog:hover {
        text-decoration: underline;
    }

    .back-to-blog i {
        margin-right: 0.5rem;
    }

    .bible-verse {
        background-color: rgba(var(--secondary-rgb), 0.1);
        border-left: 4px solid var(--primary-custom);
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0.25rem;
    }

    .bible-verse footer {
        text-align: right;
        font-weight: bold;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="post-container">
    <a href="/" class="back-to-blog">
        <i class="fas fa-arrow-left"></i> Back to Blog
    </a>

    <article>
        <header class="post-header">
            <h1 class="post-title">{{ post.title }}</h1>
            <div class="post-meta">
                <div class="flex items-center">
                    {% if post.author and post.author.profile_picture %}
                        <img src="{{ post.author.profile_picture }}" alt="{{ post.author.username }}" class="post-author-avatar">
                    {% else %}
                        <div class="post-author-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                    {% endif %}
                    <div>
                        <span class="post-author-name">
                            {% if post.author %}
                                {{ post.author.username }}
                            {% elif post.user_id %}
                                User {{ post.user_id }}
                            {% else %}
                                Anonymous
                            {% endif %}
                        </span>
                        <div class="post-date">
                            {{ post.created_at.strftime('%B %d, %Y') if post.created_at else 'Unknown date' }}
                        </div>
                    </div>
                </div>

                {% if post.category %}
                    <span class="post-category">
                        {{ post.category }}
                    </span>
                {% endif %}

                {% if post.is_featured %}
                    <span class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        <i class="fas fa-star text-yellow-500 mr-1"></i> Featured
                    </span>
                {% endif %}
            </div>
        </header>

        <div class="post-body">
            <div class="post-content">
                {{ post.content|safe }}
            </div>

            <div class="post-actions">
                <!-- Comment button -->
                <button class="post-action-button" onclick="toggleComments('{{ post.id }}')">
                    <i class="far fa-comment"></i> Comment
                </button>

                <!-- Share button with dropdown -->
                <div class="relative" x-data="{ showShareMenu: false }">
                    <button class="post-action-button" @click="showShareMenu = !showShareMenu">
                        <i class="far fa-share-square"></i> Share
                    </button>
                    <div x-show="showShareMenu" @click.away="showShareMenu = false" class="absolute bottom-full left-0 mb-2 w-48 bg-white rounded-md shadow-lg py-1 z-10" x-cloak>
                        <button onclick="shareOnFacebook('{{ post.title }}', '/post/{{ post.id }}')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fab fa-facebook-f mr-2 text-blue-600"></i> Facebook
                        </button>
                        <button onclick="shareOnTwitter('{{ post.title }}', '/post/{{ post.id }}')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fab fa-twitter mr-2 text-blue-400"></i> Twitter
                        </button>
                        <button onclick="shareOnWhatsApp('{{ post.title }}', '/post/{{ post.id }}')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fab fa-whatsapp mr-2 text-green-500"></i> WhatsApp
                        </button>
                        <button onclick="shareViaEmail('{{ post.title }}', '/post/{{ post.id }}')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="far fa-envelope mr-2 text-gray-500"></i> Email
                        </button>
                    </div>
                </div>

                <!-- Bookmark button -->
                <button class="post-action-button bookmark-button" data-post-id="{{ post.id }}">
                    <i class="far fa-bookmark"></i> Bookmark
                </button>

                <!-- Download button -->
                <button class="post-action-button" onclick="downloadAsPDF('{{ post.id }}')">
                    <i class="far fa-file-pdf"></i> Download
                </button>


            </div>
        </div>
    </article>

    <!-- Related Posts Section (placeholder) -->
    <div class="mt-8">
        <h2 class="text-xl font-semibold mb-4 text-primary-custom">Related Posts</h2>
        <p class="text-gray-500 text-center py-4">Related posts will appear here in the future.</p>
    </div>

    <!-- Comments Section (placeholder) -->
    <div class="mt-8" id="comments-section-{{ post.id }}" style="display: none;">
        <h2 class="text-xl font-semibold mb-4 text-primary-custom">Comments</h2>
        <div class="bg-white p-4 rounded-lg shadow">
            <p class="text-gray-500 text-center py-4">Comments feature coming soon!</p>
        </div>
    </div>
</div>

<!-- Bible Verse Search Modal -->
<div x-data="{ showBibleModal: false, currentPostId: null, searchQuery: '', bibleVerses: [], isLoading: false }" x-cloak>
    <div x-show="showBibleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold">Add Bible Verse</h3>
                <button @click="showBibleModal = false" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <p class="text-gray-600 mb-4">
                Search for Bible verses related to this post or enter a specific reference. You can also add a verse to your comment by clicking the "Add to Comment" button.
            </p>

            <!-- Search input -->
            <div class="flex mb-4">
                <label for="bible-search" class="sr-only">Search Bible verses</label>
                <input
                    type="text"
                    id="bible-search"
                    name="bible-search"
                    x-model="searchQuery"
                    @keydown.enter="searchBibleVerses()"
                    placeholder="Search by keyword or reference (e.g., 'love' or 'John 3:16')"
                    autocomplete="off"
                    class="flex-1 p-2 border rounded-l focus:outline-none focus:ring-2 focus:ring-primary-custom"
                >
                <button
                    @click="searchBibleVerses()"
                    class="bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded-r"
                >
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- Loading indicator -->
            <div x-show="isLoading" class="text-center py-4">
                <i class="fas fa-spinner fa-spin text-primary-custom text-2xl"></i>
                <p class="mt-2 text-gray-600">Searching...</p>
            </div>

            <!-- Results -->
            <div x-show="bibleVerses.length > 0 && !isLoading" class="max-h-60 overflow-y-auto">
                <template x-for="verse in bibleVerses" :key="verse.reference">
                    <div class="mb-4 p-3 border rounded hover:bg-gray-50">
                        <p x-text="verse.text" class="mb-2"></p>
                        <div class="flex justify-between items-center">
                            <span x-text="verse.reference" class="font-bold"></span>
                            <div>
                                <a :href="verse.biblehub_link" target="_blank" class="text-blue-600 hover:underline mr-2">
                                    <i class="fas fa-external-link-alt"></i> BibleHub
                                </a>
                                <button @click="insertVerseToPost(verse)" class="bg-primary-custom hover:bg-primary-dark text-white px-2 py-1 rounded text-sm">
                                    Add to Comment
                                </button>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- No results -->
            <div x-show="searchQuery && bibleVerses.length === 0 && !isLoading" class="text-center py-4">
                <p class="text-gray-600">No verses found. Try a different search term or reference.</p>
            </div>

            <div class="mt-4 flex justify-end">
                <button @click="showBibleModal = false" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- HTML to PDF conversion -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script>
// Function to toggle comments section
function toggleComments(postId) {
    const commentsSection = document.getElementById(`comments-section-${postId}`);
    if (commentsSection) {
        if (commentsSection.style.display === 'none') {
            commentsSection.style.display = 'block';
        } else {
            commentsSection.style.display = 'none';
        }
    } else {
        alert('Comments feature will be implemented soon!');
    }
}

// Function to download post as PDF
function downloadAsPDF(postId) {
    // Find the post element
    const postElement = document.querySelector('.post-body');
    if (!postElement) {
        console.error('Post element not found');
        return;
    }

    // Clone the post element to avoid modifying the original
    const postClone = postElement.cloneNode(true);

    // Remove action buttons from the clone
    const actionButtons = postClone.querySelector('.post-actions');
    if (actionButtons) {
        actionButtons.remove();
    }

    // Set options for PDF generation
    const options = {
        margin: [10, 10, 10, 10],
        filename: `post-${postId}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

    // Generate PDF
    html2pdf().from(postClone).set(options).save();
}

// Bible verse search and insertion functions
function searchBibleVerses() {
    const query = this.searchQuery.trim();
    if (!query) return;

    this.isLoading = true;
    this.bibleVerses = [];

    // Call the Bible API
    fetch(`/api/bible/search?query=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            this.bibleVerses = data;
            this.isLoading = false;
        })
        .catch(error => {
            console.error('Error searching Bible verses:', error);
            this.isLoading = false;
        });
}

function insertVerseToPost(verse) {
    // Create a formatted verse block
    const verseHTML = `<blockquote class="bible-verse p-3 bg-secondary-custom bg-opacity-10 border-l-4 border-primary-custom my-2">
        <p>${verse.text}</p>
        <footer class="text-right font-bold mt-1">${verse.reference}</footer>
    </blockquote>`;

    // Find the comment form for this post
    const commentForm = document.querySelector(`#comment-form-${this.currentPostId}`);
    if (commentForm) {
        const textarea = commentForm.querySelector('textarea');
        if (textarea) {
            // Insert at cursor position or append to end
            const cursorPos = textarea.selectionStart;
            const textBefore = textarea.value.substring(0, cursorPos);
            const textAfter = textarea.value.substring(cursorPos);
            textarea.value = textBefore + verseHTML + textAfter;
        }
    } else {
        alert('Comment feature will be implemented soon. You can copy this verse and add it to your comment later.');
    }

    // Close the modal
    this.showBibleModal = false;
}

// Social sharing functions
function shareOnFacebook(title, url) {
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.origin + url)}&quote=${encodeURIComponent(title)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnTwitter(title, url) {
    const shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(window.location.origin + url)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(title, url) {
    const shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(title + ' ' + window.location.origin + url)}`;
    window.open(shareUrl, '_blank');
}

function shareViaEmail(title, url) {
    const subject = encodeURIComponent(title);
    const body = encodeURIComponent(`I thought you might be interested in this post: ${title}\n\n${window.location.origin + url}`);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
}
</script>
{% endblock %}
