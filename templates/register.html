{% extends "base.html" %}

{% block title %}Register for DavarTruth{% endblock %}

{% block head %}
<style>
@keyframes loading {
    from { width: 0; }
    to { width: 100%; }
}

.loading-bar {
    animation: loading 2s linear;
}
</style>
{% endblock %}

{% block content %}
<div class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md border-t-4 border-primary-custom">
    <h2 class="text-2xl font-bold mb-6 text-center">Register</h2>

    {% if error %}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        {{ error }}
    </div>
    {% endif %}

    {% if success %}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        {{ success }}
        <div id="redirect-message" class="text-sm mt-2">Redirecting in <span id="countdown">2</span> seconds...</div>
        <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div class="bg-green-500 h-2 rounded-full loading-bar"></div>
        </div>
    </div>
    <script>
        // Handle countdown and redirect
        let count = 2;
        const countdownDisplay = document.getElementById('countdown');
        const redirectUrl = "{{ redirect }}";
        
        if (countdownDisplay && redirectUrl) {
            const timer = setInterval(() => {
                count--;
                countdownDisplay.textContent = count;
                if (count <= 0) {
                    clearInterval(timer);
                    window.location.href = redirectUrl;
                }
            }, 1000);
        }
    </script>
    {% endif %}

    <form method="POST" action="/register" id="registerForm" enctype="multipart/form-data">
        <div class="mb-4">
            <label for="username" class="block text-gray-700 text-sm font-bold mb-2">Username</label>
            <input
                type="text"
                id="username"
                name="username"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
            >
        </div>

        <div class="mb-4">
            <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
            <input
                type="email"
                id="email"
                name="email"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
            >
        </div>

        <div class="mb-4">
            <label for="phone_number" class="block text-gray-700 text-sm font-bold mb-2">Phone Number</label>
            <input
                type="tel"
                id="phone_number"
                name="phone_number"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="+1234567890"
            >
        </div>

        <div class="mb-4">
            <label for="password" class="block text-gray-700 text-sm font-bold mb-2">Password</label>
            <div class="relative">
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    required
                    minlength="8"
                >
                <button
                    type="button"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                    onclick="togglePasswordVisibility('password', 'eye-icon', 'eye-off-icon')"
                >
                    <svg id="eye-icon" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <svg id="eye-off-icon" class="h-5 w-5 text-gray-500 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                    </svg>
                </button>
            </div>
            <p class="text-xs text-gray-500 mt-1">Password must be at least 8 characters long</p>
        </div>

        <div class="mb-6">
            <label for="confirm_password" class="block text-gray-700 text-sm font-bold mb-2">Confirm Password</label>
            <div class="relative">
                <input
                    type="password"
                    id="confirm_password"
                    name="confirm_password"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    required
                    minlength="8"
                >
                <button
                    type="button"
                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                    onclick="togglePasswordVisibility('confirm_password', 'eye-icon-confirm', 'eye-off-icon-confirm')"
                >
                    <svg id="eye-icon-confirm" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <svg id="eye-off-icon-confirm" class="h-5 w-5 text-gray-500 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                    </svg>
                </button>
            </div>
        </div>

        <div class="flex items-center justify-between mb-4">
            <button
                type="submit"
                class="bg-primary-custom hover:bg-primary-dark text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
                Register
            </button>
            <a href="/login" class="inline-block align-baseline font-bold text-sm text-primary-custom hover:text-primary-dark">
                Already have an account?
            </a>
        </div>

        <!-- WebAuthn (Passkey) Registration Button -->
        <div class="webauthn-support hidden">
            <p class="text-sm text-gray-600 mb-2">After registering, you can set up a passkey for passwordless login:</p>
            <button
                type="button"
                class="webauthn-register w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center justify-center"
            >
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
                </svg>
                Register with Passkey
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script type="text/javascript">
(function() {
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('registerForm');
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');
        const submitButton = form ? form.querySelector('button[type="submit"]') : null;
        const webauthnButton = document.querySelector('.webauthn-register');
        const passwordFeedback = document.createElement('div');
        passwordFeedback.className = 'text-sm mt-1';

        if (form && password && confirmPassword && submitButton) {
            // Add password feedback element
            password.parentNode.appendChild(passwordFeedback);

            // Password validation
            password.addEventListener('input', function() {
                const value = this.value;
                const hasMinLength = value.length >= 8;
                const hasNumber = /\d/.test(value);
                const hasLetter = /[a-zA-Z]/.test(value);
                
                let feedback = [];
                let isValid = true;
                
                if (!hasMinLength) {
                    feedback.push('At least 8 characters');
                    isValid = false;
                }
                if (!hasNumber) {
                    feedback.push('At least one number');
                    isValid = false;
                }
                if (!hasLetter) {
                    feedback.push('At least one letter');
                    isValid = false;
                }
                
                if (feedback.length > 0) {
                    passwordFeedback.className = 'text-sm mt-1 text-red-500';
                    passwordFeedback.textContent = 'Required: ' + feedback.join(', ');
                } else {
                    passwordFeedback.className = 'text-sm mt-1 text-green-500';
                    passwordFeedback.textContent = '✓ Password meets requirements';
                }
                
                submitButton.disabled = !isValid;
                submitButton.classList.toggle('opacity-50', !isValid);
                submitButton.classList.toggle('cursor-not-allowed', !isValid);
            });

            // Confirm password validation
            confirmPassword.addEventListener('input', function() {
                if (this.value !== password.value) {
                    this.setCustomValidity('Passwords must match');
                } else {
                    this.setCustomValidity('');
                }
            });

            // Form submission handling
            form.addEventListener('submit', function(event) {
                if (password.value !== confirmPassword.value) {
                    event.preventDefault();
                    confirmPassword.setCustomValidity('Passwords must match');
                    confirmPassword.reportValidity();
                    return false;
                }
                
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                submitButton.innerHTML = '<span class="animate-spin mr-2">⌛</span> Registering...';
            });

            // Re-enable submit button on input change after error
            const formInputs = form.querySelectorAll('input');
            formInputs.forEach(input => {
                input.addEventListener('input', function() {
                    if (submitButton.disabled && password.value.length >= 8) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                        submitButton.innerHTML = 'Register';
                    }
                });
            });
        }

        // WebAuthn button loading state
        if (webauthnButton) {
            webauthnButton.addEventListener('click', function() {
                this.disabled = true;
                this.classList.add('opacity-50', 'cursor-not-allowed');
                const originalContent = this.innerHTML;
                this.innerHTML = '<span class="animate-spin mr-2">⌛</span> Setting up passkey...';
                
                // Re-enable after 10 seconds if no response (prevent stuck state)
                setTimeout(() => {
                    if (this.disabled) {
                        this.disabled = false;
                        this.classList.remove('opacity-50', 'cursor-not-allowed');
                        this.innerHTML = originalContent;
                    }
                }, 10000);
            });
        }

        // Password visibility toggle
        window.togglePasswordVisibility = function(inputId, eyeIconId, eyeOffIconId) {
            const input = document.getElementById(inputId);
            const eyeIcon = document.getElementById(eyeIconId);
            const eyeOffIcon = document.getElementById(eyeOffIconId);
            
            if (input.type === 'password') {
                input.type = 'text';
                eyeIcon.classList.add('hidden');
                eyeOffIcon.classList.remove('hidden');
            } else {
                input.type = 'password';
                eyeIcon.classList.remove('hidden');
                eyeOffIcon.classList.add('hidden');
            }
        }
    });
})();
</script>

{% endblock %}
