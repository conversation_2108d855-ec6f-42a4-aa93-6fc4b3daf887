{% extends "base.html" %}

{% block title %}DavarTruth Blog - Living in it, Living on it{% endblock %}

{% block head %}
{{ super() }}

<!-- SEO Meta Tags -->
<meta name="description" content="DavarTruth - A Christian blog sharing faith journeys, Bible studies, and spiritual insights to help you grow in your relationship with Christ.">
<meta name="keywords" content="Christian blog, faith, Bible study, prayer, spiritual growth, Christianity, Jesus">
<meta name="author" content="DavarTruth Community">

<!-- Open Graph / Social Media Meta Tags -->
<meta property="og:title" content="DavarTruth - Christian Faith Blog">
<meta property="og:description" content="Sharing faith journeys, Bible studies, and spiritual insights to help you grow in your relationship with <PERSON>.">
<meta property="og:type" content="website">
<meta property="og:url" content="{{ request.url if request.url is defined else '' }}">
<meta property="og:image" content="/static/images/og-image.jpg">

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="DavarTruth - Christian Faith Blog">
<meta name="twitter:description" content="Sharing faith journeys, Bible studies, and spiritual insights.">
<meta name="twitter:image" content="/static/images/og-image.jpg">

<!-- Canonical URL -->
<link rel="canonical" href="{{ request.url if request.url is defined else '' }}">

<!-- Resource preloading -->
<link rel="dns-prefetch" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preload" href="/static/css/blog-combined.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="/static/css/blog-combined.css"></noscript>

<!-- Scripts -->
<script src="/static/js/simple-html-fix.js"></script>
<script src="/static/js/alpine-init-helper.min.js"></script>
<script src="/static/js/alpine-component-functions.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js" defer></script>
<script src="/static/js/alpine-components-fixed.min.js" defer></script>
<script src="/static/js/defer-loading.min.js" defer></script>
<script src="/static/js/app.js" defer></script>

<!-- Critical CSS -->
<style>
/* Layout */
.blog-with-sidebar {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
}

/* Sidebars */
.blog-sidebar-left {
    position: sticky;
    top: 1rem;
    height: fit-content;
}

.blog-sidebar-right {
    position: sticky;
    top: 2rem;
    height: calc(100vh - 4rem);
    overflow-y: auto;
    padding-right: 1rem;
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.blog-sidebar-right::-webkit-scrollbar {
    display: none;
}

/* Content */
.blog-main-content {
    width: 100%;
    min-width: 0;
}

.post-content img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem auto;
    border-radius: 0.375rem;
    width: auto !important;
    max-height: 300px;
    object-fit: contain;
}

.post-content * {
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    box-sizing: border-box;
}

.post-content pre, .post-content code {
    white-space: pre-wrap;
    overflow-x: auto;
}

/* Responsive */
@media (max-width: 1200px) {
    .blog-with-sidebar {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    .blog-sidebar-left,
    .blog-sidebar-right {
        position: static;
        height: auto;
        width: 100%;
    }
}
@media (max-width: 1024px) {
    .blog-with-sidebar {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    .blog-sidebar-right {
        position: static;
        height: auto;
        overflow-y: visible;
    }
}
@media (max-width: 640px) {
    .post-content img {
        max-height: 200px !important;
    }
}

/* Gallery styles */
.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    aspect-ratio: 1;
    cursor: pointer;
    transition: transform 0.3s ease;
}
.gallery-item:hover {
    transform: scale(1.05);
}
.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    will-change: transform;
}
.gallery-item-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(92, 14, 20, 0.7);
    color: white;
    padding: 0.5rem;
    font-size: 0.75rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.gallery-item:hover .gallery-item-caption {
    opacity: 1;
}

/* Gallery modal */
.gallery-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}
.gallery-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}
.gallery-modal-content img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}
.gallery-modal-caption {
    color: white;
    text-align: center;
    padding: 1rem;
}
.gallery-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    color: white;
    background: rgba(0,0,0,0.5);
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.gallery-modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    background: rgba(0,0,0,0.5);
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.gallery-modal-prev {
    left: 1rem;
}
.gallery-modal-next {
    right: 1rem;
}

/* Church events/adverts styles */
.church-events {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(92, 14, 20, 0.1);
    position: relative;
}
.church-events:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    border-color: rgba(92, 14, 20, 0.2);
}
.church-events-header {
    background: var(--primary-custom);
    background-image: linear-gradient(to right, var(--primary-custom), #8a1521);
    color: white;
    padding: 0.85rem 1rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
    border-bottom: 2px solid #f0e193;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.church-events-header span {
    position: relative;
    z-index: 2;
    padding-left: 0.25rem;
    font-size: 1.15rem;
    letter-spacing: 0.5px;
    color: #f0e193;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
}
.church-events-header i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    color: #f0e193;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
}
.church-event-item {
    padding: 1.25rem;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease, transform 0.3s ease;
    position: relative;
}
.church-event-item:hover {
    background-color: #fcf8f8;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}
.church-event-item:last-child {
    border-bottom: none;
}
.church-event-date {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}
.church-event-date::before {
    content: '\f073';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.5rem;
    color: #999;
    font-size: 0.8rem;
}
.church-event-title {
    font-weight: bold;
    margin-bottom: 0.75rem;
    color: #5c0e14;
    font-size: 1.1rem;
    line-height: 1.3;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(92, 14, 20, 0.1);
    transition: color 0.2s ease;
}
.church-event-item:hover .church-event-title {
    color: #8a1521;
}
.church-event-description {
    font-size: 0.95rem;
    color: #333;
    margin-bottom: 0.75rem;
    line-height: 1.5;
}
.church-event-link {
    font-size: 0.9rem;
    color: var(--primary-custom);
    display: inline-flex;
    align-items: center;
    font-weight: 500;
    transition: color 0.2s ease;
    text-decoration: none;
    padding: 0.25rem 0;
}
.church-event-link:hover {
    color: #8a1521;
    text-decoration: underline;
}
.church-event-link i {
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}
.church-event-link:hover i {
    transform: translateX(3px);
}

/* Modal overlay styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: none;
    transition: opacity 0.3s ease;
}

.transition {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.duration-200 {
    transition-duration: 200ms;
}
.duration-300 {
    transition-duration: 300ms;
}
[x-cloak] {
    display: none !important;
}
</style>
{% endblock %}

<div class="container mx-auto px-4 blog-container blog-page" style="max-width: 1200px;">
    <!-- Main blog layout -->
    <div class="blog-with-sidebar">
        <!-- Left Sidebar -->
        <div class="blog-sidebar-left">
            <!-- About Sidebar Widget -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8 border-l-4 border-primary-custom">
                <h3 class="text-lg font-bold text-primary-custom mb-2">About DavarTruth</h3>
                <p class="text-gray-700 text-sm">
                    DavarTruth is a Christian blog sharing faith journeys, Bible studies, and spiritual insights to help you grow in your relationship with Christ. Join our community as we explore the Word together!
                </p>
            </div>
        </div>
        <!-- Main Content -->
        <main class="blog-main-content">
            <h1 class="text-3xl font-bold mb-6 text-primary-custom">DavarTruth Blog</h1>

            <!-- Debug information -->
            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                <strong>Debug Info:</strong>
                Posts count: {{ posts|length if posts else 0 }} |
                User: {{ user.username if user else 'Not logged in' }} |
                Categories: {{ categories|length if categories else 0 }}
            </div>

            <div class="space-y-6">
                {% for post in posts %}
                <article class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-2xl font-bold mb-2">
                        <a href="{{ url_for('post_detail', post_id=post.id) }}" class="text-primary-custom hover:underline">
                            {{ post.title }}
                        </a>
                    </h2>
                    <div class="text-gray-600 text-sm mb-2">
                        By {{ post.author.username if post.author else 'Anonymous' }} &middot;
                        {{ post.created_at.strftime('%B %d, %Y') if post.created_at else 'Unknown date' }}
                        {% if post.category %}
                        &middot; <span class="bg-gray-100 px-2 py-1 rounded text-xs">{{ post.category }}</span>
                        {% endif %}
                    </div>
                    <div class="post-content mb-4">
                        {{ post.content|striptags|truncate(250) }}
                    </div>
                    <a href="{{ url_for('post_detail', post_id=post.id) }}" class="text-primary-custom font-semibold hover:underline">Read More &rarr;</a>
                </article>
                {% else %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <strong>No blog posts found.</strong> This could be because:
                    <ul class="list-disc list-inside mt-2">
                        <li>No posts exist in the database</li>
                        <li>Posts are not published (is_published = False)</li>
                        <li>Posts are marked as drafts (is_draft = True)</li>
                        <li>There's an issue with the database query</li>
                    </ul>
                </div>
                {% endfor %}
            </div>
        </main>
        <!-- Right Sidebar -->
        <div class="blog-sidebar-right">
            {% include "components/donation_box.html" %}
        </div>
    </div>
</div>

<!-- Emergency close button for modals -->
<div id="modal-emergency-close" class="church-events-modal-emergency-close" style="display: none;" onclick="closeAllModals()">
    <i class="fas fa-times"></i> Close Modal
</div>

<!-- Break Reminder Modal -->
<div x-data="{
    showBreakReminder: false,
    breakReminderInterval: 60 * 60 * 1000,
    breakReminderTimer: null,
    lastBreakTime: null,
    init() {
        console.log('Break reminder component initialized');
        this.initBreakReminder();
    },
    initBreakReminder() {
        const storedLastBreakTime = localStorage.getItem('lastBreakTime');
        if (storedLastBreakTime) {
            this.lastBreakTime = parseInt(storedLastBreakTime);
            const timeElapsed = Date.now() - this.lastBreakTime;
            if (timeElapsed < this.breakReminderInterval) {
                const remainingTime = this.breakReminderInterval - timeElapsed;
                this.breakReminderTimer = setTimeout(() => {
                    this.showBreakReminder = true;
                }, remainingTime);
            } else {
                this.showBreakReminder = true;
            }
        } else {
            this.breakReminderTimer = setTimeout(() => {
                this.showBreakReminder = true;
            }, this.breakReminderInterval);
        }
    },
    takeBreak() {
        this.showBreakReminder = false;
        this.lastBreakTime = Date.now();
        localStorage.setItem('lastBreakTime', this.lastBreakTime);
        clearTimeout(this.breakReminderTimer);
        this.breakReminderTimer = setTimeout(() => {
            this.showBreakReminder = true;
        }, this.breakReminderInterval);
    },
    continueReading() {
        this.showBreakReminder = false;
        clearTimeout(this.breakReminderTimer);
        this.breakReminderTimer = setTimeout(() => {
            this.showBreakReminder = true;
        }, 15 * 60 * 1000);
    }
}">
    <!-- Break reminder overlay -->
    <div
        class="break-reminder-overlay"
        x-show="showBreakReminder"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
    ></div>
    <!-- Break reminder modal -->
    <div
        class="break-reminder"
        x-show="showBreakReminder"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-95"
        x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-95"
    >
        <div class="text-center">
            <div class="flex justify-center mb-4">
                <i class="fas fa-coffee text-4xl text-primary-custom"></i>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-800">Time for a Break</h3>
            <p class="text-gray-600 mb-4">You've been reading for an hour now. Taking regular breaks is good for your spiritual and physical well-being.</p>
            <div class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 justify-center">
                <button
                    @click="takeBreak()"
                    class="bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded font-medium"
                >
                    <i class="fas fa-check mr-2"></i>Take a Break
                </button>
                <button
                    @click="continueReading()"
                    class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded font-medium"
                >
                    <i class="fas fa-book-reader mr-2"></i>Continue Reading
                </button>
            </div>
            <p class="text-xs text-gray-500 mt-4">"Come to me, all you who are weary and burdened, and I will give you rest." - Matthew 11:28</p>
        </div>
    </div>
</div>
