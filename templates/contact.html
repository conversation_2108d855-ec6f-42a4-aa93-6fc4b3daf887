{% extends "base.html" %}

{% block title %}Contact DavarTruth - Get in Touch{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="/static/css/blog.css">
<link rel="stylesheet" href="/static/css/z-index-fix.css">
<link rel="stylesheet" href="/static/css/navbar.css">
<link rel="stylesheet" href="/static/css/blog-components-fixed.css">
<link rel="stylesheet" href="/static/css/contacts.css">
<style>
    [x-cloak] { display: none !important; }

    .contact-section {
        margin-bottom: 3rem;
    }

    .contact-card {
        background-color: #FCF8D6;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .contact-form label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .contact-form input,
    .contact-form textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 1.5rem;
    }

    .contact-form textarea {
        min-height: 150px;
    }

    .contact-info {
        background-color: rgba(92, 14, 20, 0.05);
        padding: 1.5rem;
        border-radius: 8px;
    }

    .contact-info-item {
        margin-bottom: 1rem;
        display: flex;
        align-items: flex-start;
    }

    .contact-info-item i {
        color: var(--primary-custom);
        margin-right: 0.75rem;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-8" x-data="contactsApp()">
    <h1 class="text-3xl font-bold mb-6 text-center">Contact Us</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Contact Form -->
        <div class="contact-section">
            <div class="contact-card">
                <h2 class="text-2xl font-bold mb-4">Send Us a Message</h2>
                <p class="mb-4">Have a question or feedback? We'd love to hear from you. Fill out the form below and we'll get back to you as soon as possible.</p>

                <form class="contact-form">
                    <div>
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div>
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div>
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>

                    <div>
                        <label for="message">Message</label>
                        <textarea id="message" name="message" required></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary-custom w-full">
                        <i class="fas fa-paper-plane mr-2"></i>Send Message
                    </button>
                </form>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="contact-section">
            <div class="contact-card">
                <h2 class="text-2xl font-bold mb-4">Contact Information</h2>
                <p class="mb-4">You can reach out to us through any of the following methods:</p>

                <div class="contact-info mt-6">
                    <div class="contact-info-item">
                        <i class="fas fa-envelope fa-lg"></i>
                        <div>
                            <h3 class="font-bold">Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <i class="fas fa-phone fa-lg"></i>
                        <div>
                            <h3 class="font-bold">Phone</h3>
                            <p>(*************</p>
                        </div>
                    </div>

                    <div class="contact-info-item">
                        <i class="fas fa-map-marker-alt fa-lg"></i>
                        <div>
                            <h3 class="font-bold">Address</h3>
                            <p>123 Faith Street<br>Believers City, BC 12345</p>
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h3 class="text-xl font-bold mb-3">Connect with Other Users</h3>
                    <p class="mb-4">Want to connect with other members of our community? Add them to your contacts to start a conversation.</p>

                    <button class="btn btn-secondary-custom w-full" @click="showAddContactModal = true">
                        <i class="fas fa-user-plus mr-2"></i>Add a Contact
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Contact Modal -->
    <div class="modal fade" id="addContactModal" tabindex="-1" x-show="showAddContactModal" x-cloak>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary-custom text-white">
                    <h5 class="modal-title">Add New Contact</h5>
                    <button type="button" class="btn-close btn-close-white" @click="showAddContactModal = false"></button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="addContact">
                        <div class="mb-3">
                            <label for="contactUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="contactUsername" x-model="newContact.username" required>
                            <div class="form-text">Enter the username of the person you want to add</div>
                        </div>
                        <div class="mb-3">
                            <label for="contactNickname" class="form-label">Nickname (Optional)</label>
                            <input type="text" class="form-control" id="contactNickname" x-model="newContact.nickname">
                        </div>
                        <div class="mb-3">
                            <label for="contactPhone" class="form-label">Phone (Optional)</label>
                            <input type="text" class="form-control" id="contactPhone" x-model="newContact.phone">
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" @click="showAddContactModal = false">Cancel</button>
                            <button type="submit" class="btn btn-primary-custom" :disabled="isSubmitting">
                                <span x-show="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Add Contact
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}
{% include "includes/blog_footer.html" %}
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="/static/js/contacts.js"></script>
<script>
    // Initialize Bootstrap modals
    document.addEventListener('DOMContentLoaded', function() {
        // Add Contact Modal
        const addContactModal = new bootstrap.Modal(document.getElementById('addContactModal'));

        // Watch for Alpine.js property changes to show/hide modal
        Alpine.effect(() => {
            if (Alpine.store('contactsApp').showAddContactModal) {
                addContactModal.show();
            } else {
                addContactModal.hide();
            }
        });

        // Handle modal hidden event
        document.getElementById('addContactModal').addEventListener('hidden.bs.modal', function () {
            Alpine.store('contactsApp').showAddContactModal = false;
        });
    });
</script>
{% endblock %}
