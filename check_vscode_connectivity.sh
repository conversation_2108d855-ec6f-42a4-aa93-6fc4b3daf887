#!/bin/bash

echo "VS Code Marketplace Connectivity Check"
echo "====================================="

# Define marketplace endpoints to check
ENDPOINTS=(
  "marketplace.visualstudio.com"
  "www.vscode-unpkg.net"
  "update.code.visualstudio.com"
  "vscode.blob.core.windows.net"
  "az764295.vo.msecnd.net"
)

# Check DNS resolution
echo "Checking DNS resolution..."
for endpoint in "${ENDPOINTS[@]}"; do
  echo -n "  $endpoint: "
  if host "$endpoint" > /dev/null 2>&1; then
    echo "✓ (Resolves)"
  else
    echo "✗ (DNS resolution failed)"
  fi
done

echo ""

# Check HTTPS connectivity
echo "Checking HTTPS connectivity..."
for endpoint in "${ENDPOINTS[@]}"; do
  echo -n "  $endpoint: "
  if curl -s --head --connect-timeout 10 "https://$endpoint" > /dev/null 2>&1; then
    echo "✓ (Accessible)"
  else
    echo "✗ (Connection failed)"
  fi
done

echo ""

# Check specific marketplace API endpoints
echo "Checking VS Code Marketplace API endpoints..."
API_ENDPOINTS=(
  "https://marketplace.visualstudio.com/_apis/public/gallery/extensionquery"
  "https://marketplace.visualstudio.com/_apis/public/gallery/publishers"
  "https://www.vscode-unpkg.net/_gallery/v3/extensions"
)

for api in "${API_ENDPOINTS[@]}"; do
  echo -n "  $api: "
  HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$api")
  if [[ "$HTTP_CODE" -ge 200 && "$HTTP_CODE" -lt 400 ]]; then
    echo "✓ (HTTP $HTTP_CODE)"
  else
    echo "✗ (HTTP $HTTP_CODE)"
  fi
done

echo ""

# Check for proxy settings
echo "Checking proxy settings..."
if [ -n "$http_proxy" ] || [ -n "$https_proxy" ]; then
  echo "  Proxy detected:"
  [ -n "$http_proxy" ] && echo "  HTTP Proxy: $http_proxy"
  [ -n "$https_proxy" ] && echo "  HTTPS Proxy: $https_proxy"
  [ -n "$no_proxy" ] && echo "  No Proxy: $no_proxy"
else
  echo "  No proxy settings detected in environment"
fi

# Check VS Code proxy settings
VSCODE_SETTINGS="$HOME/.config/Code/User/settings.json"
if [ -f "$VSCODE_SETTINGS" ]; then
  echo ""
  echo "Checking VS Code proxy settings in $VSCODE_SETTINGS..."
  if grep -q "http.proxy" "$VSCODE_SETTINGS"; then
    echo "  VS Code proxy settings found:"
    grep -A 3 "http.proxy" "$VSCODE_SETTINGS"
  else
    echo "  No VS Code proxy settings found"
  fi
fi

echo ""
echo "Connectivity check complete."
echo ""
echo "If you're experiencing marketplace connectivity issues:"
echo "1. If DNS resolution fails, check your network DNS settings"
echo "2. If HTTPS connectivity fails, check your firewall or network restrictions"
echo "3. Consider adding the following to your VS Code settings.json:"
echo ""
echo '{
  "http.proxySupport": "on",
  "http.proxyStrictSSL": false,
  "extensions.autoCheckUpdates": false,
  "update.mode": "manual"
}'
