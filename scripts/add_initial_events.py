from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models import Event, User
from app.database import Base

# Create database engine
engine = create_engine('sqlite:///app.db')
Base.metadata.create_all(bind=engine)

# Create session
Session = sessionmaker(bind=engine)
session = Session()

# Get first admin user as organizer
admin = session.query(User).filter_by(is_admin=True).first()
if not admin:
    print("No admin user found. Please create an admin user first.")
    sys.exit(1)

# Sample events
events = [
    {
        "title": "Sunday Worship Service",
        "description": "Join us for our weekly worship service. All are welcome!",
        "event_date": datetime.now() + timedelta(days=2),
        "location": "Main Sanctuary",
        "link": "/events/sunday-service"
    },
    {
        "title": "Bible Study Group",
        "description": "Weekly Bible study focusing on the Book of Romans.",
        "event_date": datetime.now() + timedelta(days=4),
        "location": "Fellowship Hall",
        "link": "/events/bible-study"
    },
    {
        "title": "Youth Ministry Night",
        "description": "Special event for youth with worship, games, and fellowship.",
        "event_date": datetime.now() + timedelta(days=7),
        "location": "Youth Center",
        "link": "/events/youth-night"
    },
    {
        "title": "Prayer and Worship Evening",
        "description": "A night dedicated to prayer and worship.",
        "event_date": datetime.now() + timedelta(days=9),
        "location": "Chapel",
        "link": "/events/prayer-evening"
    },
    {
        "title": "Community Outreach",
        "description": "Join us as we serve our local community through various activities.",
        "event_date": datetime.now() + timedelta(days=14),
        "location": "Community Center",
        "link": "/events/outreach"
    }
]

# Add events to database
for event_data in events:
    event = Event(
        title=event_data["title"],
        description=event_data["description"],
        event_date=event_data["event_date"],
        location=event_data["location"],
        link=event_data["link"],
        organizer_id=admin.id
    )
    session.add(event)

try:
    session.commit()
    print("Successfully added sample events!")
except Exception as e:
    session.rollback()
    print(f"Error adding events: {e}")
finally:
    session.close()
