#!/usr/bin/env python3
"""
Script to optimize images for the website.
This script will:
1. Compress JPEG and PNG images
2. Create responsive image versions
"""

import os
import sys
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor

try:
    from PIL import Image
except ImportError:
    print("Error: <PERSON>llow library not found. Please install:")
    print("  pip install Pillow")
    sys.exit(1)

# Get the static directory
SCRIPT_DIR = Path(__file__).resolve().parent
STATIC_DIR = SCRIPT_DIR.parent / 'static'
IMAGE_DIRS = [
    STATIC_DIR / 'images',
    STATIC_DIR / 'img',
    STATIC_DIR / 'assets' / 'images',
]

# Ensure image directories exist
for dir_path in IMAGE_DIRS:
    if not dir_path.exists():
        print(f"Warning: Directory {dir_path} does not exist, skipping.")
        IMAGE_DIRS.remove(dir_path)

# Define image sizes for responsive images
SIZES = [
    (320, 'sm'),
    (640, 'md'),
    (1024, 'lg'),
    (1920, 'xl')
]

def optimize_image(file_path):
    """Optimize an image using <PERSON>llow."""
    print(f"Optimizing image: {file_path}")

    try:
        img = Image.open(file_path)

        # Create a temporary path for the optimized image
        optimized_path = file_path.with_name(f"{file_path.stem}_optimized{file_path.suffix}")

        # Save with optimization
        if file_path.suffix.lower() in ['.jpg', '.jpeg']:
            img.save(optimized_path, 'JPEG', quality=85, optimize=True)
        elif file_path.suffix.lower() == '.png':
            img.save(optimized_path, 'PNG', optimize=True)
        else:
            img.save(optimized_path)

        # Check if the optimized file is smaller
        original_size = file_path.stat().st_size
        optimized_size = optimized_path.stat().st_size

        if optimized_size < original_size:
            # Replace the original with the optimized version
            optimized_path.replace(file_path)
            print(f"  Reduced size from {original_size/1024:.1f}KB to {optimized_size/1024:.1f}KB ({(1-optimized_size/original_size)*100:.1f}% reduction)")
        else:
            # Remove the optimized version if it's not smaller
            optimized_path.unlink()
            print(f"  Already optimized (no size reduction)")

        return file_path
    except Exception as e:
        print(f"  Error optimizing {file_path}: {e}")
        return None

def create_responsive_image(file_path, width, suffix):
    """Create a responsive version of an image using Pillow."""
    output_path = file_path.with_name(f"{file_path.stem}_{suffix}{file_path.suffix}")
    print(f"Creating responsive image: {output_path} ({width}px)")

    # Skip if responsive image already exists and is newer than the source
    if output_path.exists() and output_path.stat().st_mtime > file_path.stat().st_mtime:
        print(f"  Responsive image already up to date: {output_path}")
        return output_path

    try:
        img = Image.open(file_path)

        # Calculate new height to maintain aspect ratio
        width_percent = width / float(img.size[0])
        height = int(float(img.size[1]) * width_percent)

        # Resize the image
        resized_img = img.resize((width, height), Image.LANCZOS)

        # Save the resized image
        if file_path.suffix.lower() in ['.jpg', '.jpeg']:
            resized_img.save(output_path, 'JPEG', quality=85, optimize=True)
        elif file_path.suffix.lower() == '.png':
            resized_img.save(output_path, 'PNG', optimize=True)
        else:
            resized_img.save(output_path)

        print(f"  Created responsive image: {output_path}")
        return output_path
    except Exception as e:
        print(f"  Error creating responsive image {output_path}: {e}")
        return None

def process_image(file_path):
    """Process a single image file."""
    file_path = Path(file_path)
    suffix = file_path.suffix.lower()

    try:
        # Skip if not a supported image format
        if suffix not in ['.jpg', '.jpeg', '.png']:
            print(f"Skipping unsupported format: {file_path}")
            return False

        # Optimize the image
        optimize_image(file_path)

        # Create responsive versions
        for width, size_suffix in SIZES:
            create_responsive_image(file_path, width, size_suffix)

        return True
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to optimize all images."""
    image_files = []

    # Find all image files
    for dir_path in IMAGE_DIRS:
        if not dir_path.exists():
            continue

        for ext in ['.jpg', '.jpeg', '.png']:
            image_files.extend(dir_path.glob(f"**/*{ext}"))

    if not image_files:
        print("No images found to optimize.")
        return

    print(f"Found {len(image_files)} images to process.")

    # Process images in parallel
    with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
        results = list(executor.map(process_image, image_files))

    success_count = results.count(True)
    print(f"Processed {success_count} of {len(image_files)} images successfully.")

if __name__ == "__main__":
    main()
