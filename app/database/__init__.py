"""Database configuration and initialization module."""
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from typing import Generator, AsyncGenerator, AsyncContextManager
from contextlib import asynccontextmanager
import os

# Database configuration for SQLite
DATABASE_URL = "sqlite:///davartruth.db"
ASYNC_DATABASE_URL = "sqlite+aiosqlite:///davartruth.db"

# Create engines with SQLite optimizations
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})

async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    connect_args={"check_same_thread": False}
)

# Create session factories
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)

# Create base class for declarative models
Base = declarative_base()

def init_db():
    """Initialize the database by creating all tables."""
    # Import models here to avoid circular imports
    from app.database.models import Base
    Base.metadata.create_all(bind=engine)

def get_db() -> Generator[Session, None, None]:
    """Get a database session as a generator."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@asynccontextmanager
async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Get an async database session as an async context manager.
    
    Usage:
        async with get_async_db() as db:
            result = await db.execute(...)
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# Alias for backward compatibility
get_db_context = get_async_db

# SQLite optimizations
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    cursor = dbapi_connection.cursor()
    cursor.execute("PRAGMA journal_mode=WAL")
    cursor.execute("PRAGMA foreign_keys=ON")
    cursor.execute("PRAGMA page_size=4096")
    cursor.execute("PRAGMA cache_size=-2000")
    cursor.execute("PRAGMA mmap_size=8589934592")
    cursor.close()

# Export all the necessary components
__all__ = [
    'engine',
    'async_engine',
    'SessionLocal',
    'AsyncSessionLocal',
    'Base', 
    'init_db',
    'get_db',
    'get_async_db',
    'get_db_context'  # Include the alias in exports
]
