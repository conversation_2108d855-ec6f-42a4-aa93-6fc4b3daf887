from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import random
from datetime import datetime

from ..database import get_db
from ..services.bible_service import get_verse, get_verse_by_keyword

router = APIRouter()

# Daily verses to choose from when no database verse is available
DAILY_VERSES = [
    {
        "reference": "John 3:16",
        "text": "For God so loved the world, that he gave his only Son, that whoever believes in him should not perish but have eternal life.",
        "translation": "ESV"
    },
    {
        "reference": "Philippians 4:13",
        "text": "I can do all things through him who strengthens me.",
        "translation": "ESV"
    },
    {
        "reference": "Jeremiah 29:11",
        "text": "For I know the plans I have for you, declares the LORD, plans for welfare and not for evil, to give you a future and a hope.",
        "translation": "ESV"
    },
    {
        "reference": "Romans 8:28",
        "text": "And we know that for those who love God all things work together for good, for those who are called according to his purpose.",
        "translation": "ESV"
    },
    {
        "reference": "Psalm 23:1",
        "text": "The LORD is my shepherd; I shall not want.",
        "translation": "ESV"
    }
]

@router.get("/api/daily-verse")
async def get_daily_verse(db: AsyncSession = Depends(get_db)):
    """Get the verse of the day"""
    try:
        # Use today's date as a seed for random selection
        today = datetime.now()
        seed = today.year * 10000 + (today.month + 1) * 100 + today.day
        random.seed(seed)
        
        # Try to get a verse from the database first
        seed_verse = DAILY_VERSES[seed % len(DAILY_VERSES)]
        verse = await get_verse(seed_verse["reference"], db)
        
        if not verse:
            # If database lookup fails, use the pre-defined verse
            verse = seed_verse
        
        # Add BibleHub link
        verse["biblehub_link"] = f"https://biblehub.com/{verse['reference'].lower().replace(' ', '').replace(':', '/')}.htm"
        
        return verse
    except Exception as e:
        print(f"Error getting daily verse: {e}")
        # Return the first predefined verse as a fallback
        return DAILY_VERSES[0]
