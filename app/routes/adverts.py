from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime

from ..database import get_db
from ..models import Advert

router = APIRouter()

@router.get("/api/adverts")
async def get_adverts(db: AsyncSession = Depends(get_db)):
    """Get all active adverts"""
    try:
        adverts_query = select(Advert).where(Advert.expiry_date >= datetime.now()).order_by(Advert.created_at.desc())
        result = await db.execute(adverts_query)
        adverts = result.scalars().all()
        
        return {
            "adverts": [
                {
                    "id": advert.id,
                    "title": advert.title,
                    "description": advert.description,
                    "link": advert.link,
                    "linkText": advert.link_text,
                    "expiryDate": advert.expiry_date.isoformat()
                }
                for advert in adverts
            ]
        }
    except Exception as e:
        print(f"Error getting adverts: {e}")
        return {"adverts": []}
