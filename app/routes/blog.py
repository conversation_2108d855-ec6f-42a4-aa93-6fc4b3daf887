from fastapi import APIRouter, Depends, Request, Form, status, Body, HTTPException
from fastapi.responses import RedirectResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func, text
from typing import List, Dict, Any, Optional
from ..database import get_db
from ..models import BlogPost, User, BlogComment, ContentReport, Event, PrayerRequest
from ..utils.content_moderation import check_content, get_violation_message
from ..scheduled_tasks import publish_scheduled_posts
from ..db_utils import execute_safe_query
from ..template_config import templates
import bleach
from datetime import datetime
from types import SimpleNamespace

# Get the allowed tags and attributes from bleach
ALLOWED_TAGS = bleach.sanitizer.ALLOWED_TAGS
ALLOWED_ATTRIBUTES = bleach.sanitizer.ALLOWED_ATTRIBUTES

router = APIRouter()

# Create custom allowed HTML tags and attributes for rich content
CUSTOM_ALLOWED_TAGS = set(ALLOWED_TAGS) | {
    'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre', 'img', 'span', 'div', 'br', 'hr',
    'table', 'thead', 'tbody', 'tr', 'th', 'td', 'blockquote', 'strong', 'em', 'u', 's',
    'ol', 'ul', 'li', 'code', 'figure', 'figcaption', 'footer'
}

# Create a copy of the allowed attributes and update it
CUSTOM_ALLOWED_ATTRIBUTES = dict(ALLOWED_ATTRIBUTES)
CUSTOM_ALLOWED_ATTRIBUTES.update({
    'a': ['href', 'title', 'target', 'rel', 'class', 'style'],
    'img': ['src', 'alt', 'title', 'width', 'height', 'style', 'class', 'data-*'],
    'span': ['style', 'class'],
    'div': ['style', 'class'],
    'p': ['style', 'class'],
    'h1': ['style', 'class'],
    'h2': ['style', 'class'],
    'h3': ['style', 'class'],
    'h4': ['style', 'class'],
    'h5': ['style', 'class'],
    'h6': ['style', 'class'],
    'blockquote': ['style', 'class'],
    'table': ['style', 'class', 'border'],
    'th': ['style', 'class', 'scope', 'colspan', 'rowspan'],
    'td': ['style', 'class', 'colspan', 'rowspan'],
    'figure': ['style', 'class'],
    'figcaption': ['style', 'class'],
    'footer': ['style', 'class'],
    '*': ['class', 'style']
})

# API endpoints for post management
@router.delete("/api/posts/{post_id}")
async def delete_post(post_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    try:
        # Check if user is logged in
        user = request.state.user
        if not user:
            raise HTTPException(status_code=401, detail="Authentication required")

        # Get the post
        query = select(BlogPost).where(BlogPost.id == post_id)
        result = await db.execute(query)
        post = result.scalars().first()

        # Check if post exists
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        # Check if user is the author
        if post.user_id != user.id:
            raise HTTPException(status_code=403, detail="You don't have permission to delete this post")

        # Delete the post using raw SQL to avoid ORM issues
        from sqlalchemy import text
        delete_query = text(f"DELETE FROM blog_posts WHERE id = :post_id")
        await db.execute(delete_query, {"post_id": post_id})
        await db.commit()

        return {"success": True}
    except Exception as e:
        # Log the error
        print(f"Error deleting post {post_id}: {str(e)}")
        # Rollback the transaction
        await db.rollback()
        # Return a 500 error
        raise HTTPException(status_code=500, detail=f"Error deleting post: {str(e)}")

@router.get("/edit-post/{post_id}")
async def edit_post_page(post_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        return RedirectResponse(url=f"/login?next=/edit-post/{post_id}", status_code=status.HTTP_302_FOUND)

    # Get the post
    query = select(BlogPost).where(BlogPost.id == post_id)
    result = await db.execute(query)
    post = result.scalars().first()

    # Check if post exists
    if not post:
        return RedirectResponse(url="/author-dashboard", status_code=status.HTTP_302_FOUND)

    # Check if user is the author
    if post.user_id != user.id:
        return RedirectResponse(url="/author-dashboard", status_code=status.HTTP_302_FOUND)

    return templates.TemplateResponse(
        "edit_post.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": True,
            "post": post
        }
    )

@router.post("/api/posts/{post_id}/publish-now")
async def publish_post_now(post_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Get the post
    query = select(BlogPost).where(BlogPost.id == post_id)
    result = await db.execute(query)
    post = result.scalars().first()

    # Check if post exists
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    # Check if user is the author
    if post.user_id != user.id:
        raise HTTPException(status_code=403, detail="You don't have permission to publish this post")

    # Update post to be published immediately
    post.is_published = True
    post.is_draft = False  # Ensure it's not a draft
    post.scheduled_publish_date = None

    # Save changes
    await db.commit()

    return {"success": True, "redirect": "/author-dashboard"}

@router.post("/api/drafts/{draft_id}/publish")
async def publish_draft(draft_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Get the draft
    query = select(BlogPost).where(BlogPost.id == draft_id, BlogPost.is_draft == True)
    result = await db.execute(query)
    draft = result.scalars().first()

    # Check if draft exists
    if not draft:
        raise HTTPException(status_code=404, detail="Draft not found")

    # Check if user is the author
    if draft.user_id != user.id:
        raise HTTPException(status_code=403, detail="You don't have permission to publish this draft")

    # Update draft to be a published post
    draft.is_published = True
    draft.is_draft = False
    draft.scheduled_publish_date = None

    # Save changes
    await db.commit()

    return {"success": True, "redirect": "/author-dashboard"}

@router.get("/edit-draft/{draft_id}")
async def edit_draft_page(draft_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        return RedirectResponse(url=f"/login?next=/edit-draft/{draft_id}", status_code=status.HTTP_302_FOUND)

    # Get the draft
    query = select(BlogPost).where(BlogPost.id == draft_id, BlogPost.is_draft == True)
    result = await db.execute(query)
    draft = result.scalars().first()

    # Check if draft exists
    if not draft:
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "Draft not found.",
                "user": user,
                "is_authenticated": True
            },
            status_code=status.HTTP_404_NOT_FOUND
        )

    # Check if user is the author
    if draft.user_id != user.id:
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "You don't have permission to edit this draft.",
                "user": user,
                "is_authenticated": True
            },
            status_code=status.HTTP_403_FORBIDDEN
        )

    # Get current datetime for the datetime-local input min value
    now = datetime.now()

    return templates.TemplateResponse(
        "edit_draft.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": True,
            "draft": draft,
            "now": now
        }
    )

@router.post("/edit-draft/{draft_id}")
async def update_draft(
    draft_id: int,
    request: Request,
    title: str = Form(...),
    content: str = Form(...),
    category: str = Form(None),
    action: str = Form("save"),  # save, publish
    db: AsyncSession = Depends(get_db)
):
    # Check if user is logged in
    user = request.state.user
    if not user:
        return RedirectResponse(url=f"/login?next=/edit-draft/{draft_id}", status_code=status.HTTP_302_FOUND)

    # Get the draft
    query = select(BlogPost).where(BlogPost.id == draft_id, BlogPost.is_draft == True)
    result = await db.execute(query)
    draft = result.scalars().first()

    # Check if draft exists
    if not draft:
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "Draft not found.",
                "user": user,
                "is_authenticated": True
            },
            status_code=status.HTTP_404_NOT_FOUND
        )

    # Check if user is the author
    if draft.user_id != user.id:
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "You don't have permission to edit this draft.",
                "user": user,
                "is_authenticated": True
            },
            status_code=status.HTTP_403_FORBIDDEN
        )

    # Check for prohibited content
    title_allowed, title_violations = check_content(title)
    content_allowed, content_violations = check_content(content)

    if not title_allowed or not content_allowed:
        # Combine violations
        all_violations = set(title_violations or []) | set(content_violations or [])
        error_message = get_violation_message(list(all_violations))

        # Return to the edit draft page with an error message
        return templates.TemplateResponse(
            "edit_draft.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": True,
                "draft": draft,
                "error": error_message
            },
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # Sanitize HTML content
    clean_content = sanitize_html(content)

    # Update draft fields
    draft.title = title
    draft.content = clean_content
    draft.category = category
    draft.updated_at = datetime.utcnow()

    # Check if we should publish the draft
    if action == "publish":
        draft.is_published = True
        draft.is_draft = False
        success_message = "Draft published successfully!"
        redirect_url = "/"
    else:  # Just save the draft
        success_message = "Draft updated successfully!"
        redirect_url = "/author-dashboard"

    # Save changes
    await db.commit()

    # Try to set a success message in the session
    try:
        request.session["success_message"] = success_message
    except Exception as e:
        print(f"Could not set session message: {e}")

    # Redirect to the appropriate page
    return RedirectResponse(url=redirect_url, status_code=status.HTTP_302_FOUND)

@router.post("/api/drafts/{draft_id}/delete")
async def delete_draft(draft_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Get the draft
    query = select(BlogPost).where(BlogPost.id == draft_id, BlogPost.is_draft == True)
    result = await db.execute(query)
    draft = result.scalars().first()

    # Check if draft exists
    if not draft:
        raise HTTPException(status_code=404, detail="Draft not found")

    # Check if user is the author
    if draft.user_id != user.id:
        raise HTTPException(status_code=403, detail="You don't have permission to delete this draft")

    # Delete the draft
    await db.delete(draft)
    await db.commit()

    return {"success": True, "message": "Draft deleted successfully"}

@router.post("/api/posts/{post_id}/feature")
async def toggle_featured_post(post_id: int, request: Request, data: Dict[str, Any] = Body(...), db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Get the post
    try:
        query = select(BlogPost).where(BlogPost.id == post_id)
        print(f"Feature post query: {str(query)}")
        result = await db.execute(query)
        post = result.scalars().first()
    except Exception as e:
        print(f"Error fetching post for featuring: {e}")
        raise HTTPException(status_code=500, detail="Error fetching post")

    # Check if post exists
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    # Check if user is the author
    if post.user_id != user.id:
        raise HTTPException(status_code=403, detail="You don't have permission to feature this post")

    # Update featured status
    is_featured = data.get("is_featured", False)
    post.is_featured = is_featured

    # Save changes
    await db.commit()

    return {"success": True, "is_featured": post.is_featured}

@router.put("/api/posts/{post_id}")
async def update_post(post_id: int, request: Request, data: Dict[str, Any] = Body(...), db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Get the post
    try:
        query = select(BlogPost).where(BlogPost.id == post_id)
        print(f"Update post query: {str(query)}")
        result = await db.execute(query)
        post = result.scalars().first()
    except Exception as e:
        print(f"Error fetching post for update: {e}")
        raise HTTPException(status_code=500, detail="Error fetching post")

    # Check if post exists
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    # Check if user is the author
    if post.user_id != user.id:
        raise HTTPException(status_code=403, detail="You don't have permission to edit this post")

    # Update post fields
    title = data.get("title")
    content = data.get("content")

    if title:
        post.title = title

    if content:
        post.content = sanitize_html(content)

    # Save changes
    await db.commit()

    return {"success": True, "post": {
        "id": post.id,
        "title": post.title,
        "content": post.content,
        "created_at": post.created_at.isoformat() if post.created_at else None
    }}

@router.post("/api/profile")
async def update_profile(request: Request, db: AsyncSession = Depends(get_db)):
    # This is a placeholder for the profile update API
    # In a real implementation, you would process form data here
    return {"success": True}

@router.post("/api/change-password")
async def change_password(request: Request, db: AsyncSession = Depends(get_db)):
    # This is a placeholder for the password change API
    # In a real implementation, you would verify the current password and update with the new one
    return {"success": True}

@router.post("/api/check-scheduled-posts")
async def check_scheduled_posts(request: Request):
    # Check if user is logged in and is an admin
    user = request.state.user
    if not user or not getattr(user, 'is_admin', False):
        raise HTTPException(status_code=403, detail="Admin access required")

    # Manually run the scheduled posts check
    try:
        count = await publish_scheduled_posts()
        return {"success": True, "published_count": count}
    except Exception as e:
        return {"success": False, "error": str(e)}

def sanitize_html(content: str) -> str:
    """Sanitize HTML content to prevent XSS attacks while preserving images"""
    # First, clean the HTML with our custom allowed tags and attributes
    cleaned_html = bleach.clean(
        content,
        tags=CUSTOM_ALLOWED_TAGS,
        attributes=CUSTOM_ALLOWED_ATTRIBUTES,
        strip=True,
        protocols=['http', 'https', 'data'],  # Allow data URLs for embedded images
    )

    return cleaned_html

@router.get("/contact")
async def contact_page(request: Request):
    """
    Contact Us page with option to add contacts
    """
    # Get user info
    user = request.state.user
    is_authenticated = user is not None

    return templates.TemplateResponse(
        "contact.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": is_authenticated
        }
    )

@router.get("/")
async def read_blog(request: Request, db: AsyncSession = Depends(get_db)):
    # Get user info
    user = request.state.user
    is_authenticated = user is not None

    # Get daily scripture
    daily_scripture = {
        "verse": "For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life.",
        "reference": "John 3:16",
        "translation": "NIV"
    }

    # Get church events
    try:
        events_query = select(Event).where(Event.event_date >= datetime.now()).order_by(Event.event_date.asc()).limit(5)
        events_result = await db.execute(events_query)
        events = events_result.scalars().all()
        
        # Format events for template
        church_events = []
        for event in events:
            church_events.append({
                "title": event.title,
                "description": event.description,
                "date": event.event_date.strftime("%B %d, %Y at %I:%M %p"),
                "location": event.location,
                "link": event.link
            })
    except Exception as e:
        print(f"Error fetching church events: {e}")
        church_events = []

    # Get prayer requests
    try:
        prayer_query = select(PrayerRequest).order_by(PrayerRequest.created_at.desc()).limit(10)
        prayer_result = await db.execute(prayer_query)
        prayer_requests = prayer_result.scalars().all()
    except Exception as e:
        print(f"Error fetching prayer requests: {e}")
        prayer_requests = []

    # Get featured posts (only published ones)
    try:
        featured_query = select(BlogPost, User).outerjoin(User, BlogPost.user_id == User.id).where(
            (BlogPost.is_featured == True) &
            (BlogPost.is_published == True) &
            (BlogPost.is_draft == False)
        ).order_by(BlogPost.created_at.desc())

        featured_result = await db.execute(featured_query)
        featured_post_users = featured_result.fetchall()
    except Exception as e:
        print(f"Error fetching featured posts: {e}")
        featured_post_users = []

    featured_posts = []
    for post, author in featured_post_users:
        featured_posts.append({
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "created_at": post.created_at,
            "user_id": post.user_id,
            "category": post.category,
            "author": author,
            "image": getattr(post, "image", "/static/images/default-post.jpg"),
            "excerpt": post.content[:200] + "..." if len(post.content) > 200 else post.content
        })

    # Get regular posts
    try:
        query = select(BlogPost, User).outerjoin(User, BlogPost.user_id == User.id).where(
            (BlogPost.is_published == True) &
            (BlogPost.is_draft == False)
        ).order_by(BlogPost.created_at.desc())

        result = await db.execute(query)
        post_users = result.fetchall()
    except Exception as e:
        print(f"Error fetching regular posts: {e}")
        post_users = []

    posts = []
    for post, author in post_users:
        posts.append({
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "created_at": post.created_at,
            "user_id": post.user_id,
            "category": post.category,
            "author": author
        })

    # Get categories for sidebar
    try:
        # Get distinct categories from published posts
        category_query = select(func.distinct(BlogPost.category)).where(
            (BlogPost.is_published == True) &
            (BlogPost.is_draft == False) &
            (BlogPost.category != None)  # Exclude posts without categories
        )
        category_result = await db.execute(category_query)
        categories = [cat[0] for cat in category_result if cat[0]]  # Filter out None values
    except Exception as e:
        print(f"Error fetching categories: {e}")
        categories = []

    return templates.TemplateResponse(
        "blog.html",
        {
            "request": request,
            "posts": posts,
            "featured_posts": featured_posts,
            "related_posts": featured_posts,  # Use featured posts as related posts
            "categories": categories,
            "user": user,
            "is_authenticated": is_authenticated,
            "daily_scripture": daily_scripture,
            "church_events": church_events,
            "prayer_requests": prayer_requests
        }
    )

@router.get("/author-dashboard")
async def author_dashboard(request: Request, db: AsyncSession = Depends(get_db)):
    try:
        # Check if user is logged in
        user = request.state.user
        if not user:
            # Redirect to login with a next parameter to return after login
            return RedirectResponse(
                url=f"/login?next=/author-dashboard",
                status_code=status.HTTP_302_FOUND
            )

        # Get active tab from session
        active_tab = None
        try:
            active_tab = request.session.get("active_tab")
            if active_tab:
                print(f"Found active_tab in session: {active_tab}")
                # Clear it from the session so it's only used once
                request.session.pop("active_tab", None)
        except Exception as e:
            print(f"Error getting active_tab from session: {e}")

        # Initialize variables with safe defaults
        user_posts = []
        scheduled_posts = []
        total_views = 0
        total_comments = 0
        drafts = []

        # Get user's posts with error handling
        try:
            # First, check if the required columns exist
            # This is a simple query to test if the columns exist
            test_query = "SELECT is_published, scheduled_publish_date FROM blog_posts LIMIT 1"
            try:
                await db.execute(test_query)
                columns_exist = True
            except Exception:
                columns_exist = False
                print("Required columns don't exist in the database schema")

            if columns_exist:
                # Get user's published posts (excluding scheduled posts)
                published_query = select(BlogPost).where(
                    (BlogPost.user_id == user.id) &
                    (BlogPost.is_published == True) &
                    (BlogPost.is_draft == False) &  # Make sure it's not a draft
                    (BlogPost.is_scheduled == False)  # Make sure it's not a scheduled post
                ).order_by(BlogPost.created_at.desc())
                print(f"Published posts query: {str(published_query)}")
                published_result = await db.execute(published_query)
                user_posts = published_result.scalars().all()

                # Debug information for published posts
                print(f"\nFound {len(user_posts)} published posts:")
                for post in user_posts:
                    print(f"  ID: {post.id}, Title: {post.title}")
                    print(f"  is_published: {post.is_published}, is_draft: {post.is_draft}")
                    print(f"  scheduled_publish_date: {post.scheduled_publish_date}")

                # Get user's scheduled posts using the new is_scheduled field
                scheduled_query = select(BlogPost).where(
                    (BlogPost.user_id == user.id) &
                    (BlogPost.is_scheduled == True)  # Use the dedicated field
                ).order_by(BlogPost.scheduled_publish_date.asc())
                print(f"Scheduled posts query: {str(scheduled_query)}")
                scheduled_result = await db.execute(scheduled_query)
                scheduled_posts = scheduled_result.scalars().all()

                # Debug information for scheduled posts
                print(f"\nFound {len(scheduled_posts)} scheduled posts:")
                for post in scheduled_posts:
                    print(f"  ID: {post.id}, Title: {post.title}")
                    print(f"  is_published: {post.is_published}, is_draft: {post.is_draft}")
                    print(f"  scheduled_publish_date: {post.scheduled_publish_date}")
            else:
                # Fallback to simpler query if the new columns don't exist
                query = select(BlogPost).where(BlogPost.user_id == user.id).order_by(BlogPost.created_at.desc())
                result = await db.execute(query)
                user_posts = result.scalars().all()
        except Exception as e:
            print(f"Error fetching posts: {e}")
            # If all else fails, just use empty lists
            user_posts = []
            scheduled_posts = []

        # Get total views (placeholder for now)
        try:
            for post in user_posts:
                total_views += getattr(post, 'views', 0) or 0
        except Exception as e:
            print(f"Error calculating views: {e}")
            total_views = 0

        # Get total comments for user's posts
        try:
            # Get all post IDs for this user
            user_post_ids = [post.id for post in user_posts]

            if user_post_ids:
                # Count comments for these posts
                comments_query = select(func.count(BlogComment.id)).where(BlogComment.post_id.in_(user_post_ids))
                comments_result = await db.execute(comments_query)
                total_comments = comments_result.scalar() or 0
            else:
                total_comments = 0
        except Exception as e:
            print(f"Error calculating comments: {e}")
            total_comments = 0

        # Get user's draft posts
        try:
            if columns_exist:
                # Get user's draft posts
                drafts_query = select(BlogPost).where(
                    (BlogPost.user_id == user.id) &
                    (BlogPost.is_draft == True)
                ).order_by(BlogPost.updated_at.desc())
                drafts_result = await db.execute(drafts_query)
                drafts = drafts_result.scalars().all()
            else:
                drafts = []
        except Exception as e:
            print(f"Error fetching drafts: {e}")
            drafts = []

        return templates.TemplateResponse(
            "author_dashboard.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": True,
                "user_posts": user_posts,
                "scheduled_posts": scheduled_posts,
                "total_views": total_views,
                "total_comments": total_comments,
                "drafts": drafts,
                "active_tab": active_tab  # Pass the active tab to the template
            }
        )
    except Exception as e:
        print(f"Unhandled error in author_dashboard: {e}")
        # Return a simple error page instead of 500
        return templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "There was an error loading the author dashboard. Please try again later.",
                "user": getattr(request.state, 'user', None),
                "is_authenticated": getattr(request.state, 'user', None) is not None
            }
        )

@router.get("/create-post")
async def create_post_page(request: Request):
    # Check if user is logged in
    user = request.state.user
    if not user:
        # Redirect to login with a next parameter to return after login
        return RedirectResponse(
            url=f"/login?next=/create-post",
            status_code=status.HTTP_302_FOUND
        )

    # Check if user has completed profile
    if not user.has_completed_profile:
        return RedirectResponse(url="/create-profile", status_code=status.HTTP_302_FOUND)

    # Get current datetime for the datetime-local input min value
    now = datetime.now()

    return templates.TemplateResponse(
        "create_post.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": True,
            "now": now
        }
    )

@router.post("/create-post")
async def create_post(
    request: Request,
    title: str = Form(...),
    content: str = Form(...),
    category: str = Form(None),
    schedule_post: str = Form("false"),
    scheduled_date: str = Form(None),
    action: str = Form("publish"),  # New parameter for the action (publish or draft)
    db: AsyncSession = Depends(get_db)
):
    # Print debug information
    print(f"\n\nCreate Post Request:")
    print(f"Title: {title}")
    print(f"Category: {category}")
    print(f"Schedule Post: {schedule_post}")
    print(f"Scheduled Date: {scheduled_date}")
    print(f"Action: {action}")

    # Print all form data for debugging
    print("\nAll form data:")
    form_data = await request.form()
    for key, value in form_data.items():
        print(f"  {key}: {value}")
    print("\n")
    # Check if user is logged in
    user = request.state.user
    if not user:
        return RedirectResponse(url="/login?next=/create-post", status_code=status.HTTP_302_FOUND)

    # Check for prohibited content
    title_allowed, title_violations = check_content(title)
    content_allowed, content_violations = check_content(content)

    if not title_allowed or not content_allowed:
        # Combine violations
        all_violations = set(title_violations or []) | set(content_violations or [])
        error_message = get_violation_message(list(all_violations))

        # Get current datetime for the datetime-local input min value
        now = datetime.now()

        # Return to the create post page with an error message
        return templates.TemplateResponse(
            "create_post.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": True,
                "error": error_message,
                "title": title,  # Return the title so user doesn't lose their work
                "content": content,  # Return the content so user doesn't lose their work
                "now": now  # Add the current datetime
            },
            status_code=status.HTTP_400_BAD_REQUEST
        )

    # Sanitize HTML content
    clean_content = sanitize_html(content)

    # Process scheduled date if provided
    scheduled_publish_date = None
    is_published = True
    is_draft = False
    is_scheduled = False

    # Check the action
    if action == "draft":
        # Save as draft
        is_published = False
        is_draft = True
        success_message = "Post saved as draft successfully!"
        redirect_url = "/author-dashboard"
    elif action == "schedule":
        # Schedule for later
        print(f"\nScheduling post with scheduled_date: {scheduled_date}")

        # Check if schedule_post is set to true
        if schedule_post.lower() != "true":
            print(f"Warning: action is 'schedule' but schedule_post is '{schedule_post}'")

        if scheduled_date:
            try:
                # Parse the datetime-local input value
                print(f"Parsing scheduled date: {scheduled_date}")
                scheduled_publish_date = datetime.fromisoformat(scheduled_date)
                print(f"Parsed scheduled date: {scheduled_publish_date}")

                # If the scheduled date is in the future, mark as unpublished
                now = datetime.now()
                print(f"Current time: {now}")

                if scheduled_publish_date > now:
                    is_published = False
                    is_draft = False  # Make sure it's not marked as a draft
                    is_scheduled = True  # Mark as scheduled
                    redirect_url = "/author-dashboard#scheduled"
                    success_message = f"Post scheduled successfully! It will be published on {scheduled_publish_date.strftime('%B %d, %Y at %I:%M %p')}."
                    print(f"Post will be scheduled for future publication at {scheduled_publish_date}")
                else:
                    # If date is in the past, publish immediately
                    redirect_url = "/"
                    success_message = "Post published successfully! (Scheduled date was in the past)"
                    print(f"Scheduled date {scheduled_publish_date} is in the past, publishing immediately")
            except ValueError as e:
                # If date parsing fails, publish immediately
                redirect_url = "/"
                success_message = "Post published immediately (invalid scheduled date)"
                print(f"Error parsing scheduled date: {e}")
        else:
            # No scheduled date provided
            redirect_url = "/"
            success_message = "Post published immediately (no scheduled date provided)"
            print("No scheduled date provided")
    else:
        # Regular publish
        redirect_url = "/"
        success_message = "Post published successfully!"

    # Create new post with error handling
    try:
        print("Creating new post...")
        print(f"User ID: {user.id}")
        print(f"Is Published: {is_published}")
        print(f"Is Draft: {is_draft}")
        print(f"Scheduled Publish Date: {scheduled_publish_date}")

        # Try a simpler approach - create a dictionary of values first
        post_data = {
            "title": title,
            "content": clean_content,
            "user_id": user.id,
            "is_featured": False,
            "category": category,
            "scheduled_publish_date": scheduled_publish_date,
            "is_published": is_published,
            "is_scheduled": is_scheduled,
            # Only include created_at, not updated_at
            "created_at": datetime.now()
        }

        # Try to add is_draft if it exists in the model
        try:
            # Check if BlogPost has is_draft attribute
            if hasattr(BlogPost, 'is_draft'):
                post_data["is_draft"] = is_draft
                print("Added is_draft to post data")
        except Exception as e:
            print(f"Error checking for is_draft attribute: {e}")

        # Create the post object
        try:
            print("Creating BlogPost object with data:")
            for key, value in post_data.items():
                print(f"  {key}: {value}")

            # Try to create the post using raw SQL to avoid ORM issues
            from sqlalchemy import text

            # Build the column names and placeholders
            columns = list(post_data.keys())
            placeholders = [f":{col}" for col in columns]

            # Create the SQL query
            query = text(f"""
            INSERT INTO blog_posts ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            """)

            # Execute the query
            print(f"Executing SQL query: {query}")
            result = await db.execute(query, post_data)
            await db.commit()

            # Get the last inserted ID
            get_last_id_query = text("SELECT last_insert_rowid()")
            last_id_result = await db.execute(get_last_id_query)
            post_id = last_id_result.scalar()

            # Get the post object
            get_post_query = text("SELECT * FROM blog_posts WHERE id = :id")
            post_result = await db.execute(get_post_query, {"id": post_id})
            post = post_result.fetchone()

            print(f"Post created successfully with ID: {post_id}")
        except Exception as e:
            print(f"Error creating blog post: {e}")
            try:
                if db and hasattr(db, 'is_active') and db.is_active:
                    await db.rollback()
                else:
                    print("No active database session to rollback")
            except Exception as rollback_error:
                print(f"Error during rollback: {rollback_error}")
            return templates.TemplateResponse(
                "create_post.html",
                {"request": request, "error": "An error occurred while creating your post."},
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # We've already committed the post in the previous step
        print(f"Successfully created post: {title}")
    except Exception as e:
        print(f"Unexpected error creating post: {e}")
        import traceback
        traceback.print_exc()
        # Get current datetime for the datetime-local input min value
        now = datetime.now()

        # Return error to user
        return templates.TemplateResponse(
            "create_post.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": True,
                "error": f"Unexpected error: {str(e)}",
                "title": title,
                "content": content,
                "now": now  # Add the current datetime
            },
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    # Try to set a success message in the session
    try:
        request.session["success_message"] = success_message
    except Exception as e:
        print(f"Could not set session message: {e}")
        # Continue without the session message

    # Set the active tab in the session
    try:
        if action == "schedule":
            request.session["active_tab"] = "scheduled"
            print("Setting active_tab to 'scheduled' in session")
        elif action == "draft":
            request.session["active_tab"] = "drafts"
            print("Setting active_tab to 'drafts' in session")
        else:
            request.session["active_tab"] = "posts"
            print("Setting active_tab to 'posts' in session")
    except Exception as e:
        print(f"Could not set active tab in session: {e}")

    # Redirect to the appropriate page
    return RedirectResponse(url=redirect_url, status_code=status.HTTP_302_FOUND)

@router.post("/posts")
async def create_post_api(
    request: Request,
    data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_db)
):
    # This endpoint is for the AJAX form in the blog page
    print("\n\nCreate Post API Request:")
    print(f"Data: {data}\n\n")

    # Check if user is logged in
    if not request.state.user:
        return {
            "success": False,
            "error": "Authentication required",
            "redirect": "/login"
        }

    title = data.get("title", "")
    content = data.get("content", "")

    # Validate input
    if not title.strip() or not content.strip():
        return {"success": False, "error": "Title and content are required"}

    # Get user ID
    user_id = request.state.user.id

    # Check for prohibited content
    title_allowed, title_violations = check_content(title)
    content_allowed, content_violations = check_content(content)

    if not title_allowed or not content_allowed:
        # Combine violations
        all_violations = set(title_violations or []) | set(content_violations or [])
        error_message = get_violation_message(list(all_violations))
        return {"success": False, "error": error_message}

    # Sanitize HTML content
    clean_content = sanitize_html(content)

    # Process scheduled date if provided
    scheduled_publish_date = None
    is_published = True
    is_draft = data.get("is_draft", False)
    is_scheduled = False

    # Check if this is a draft
    if is_draft:
        is_published = False
    # Check if this is scheduled
    elif data.get("schedule_post") and data.get("scheduled_date"):
        try:
            # Parse the datetime string
            scheduled_publish_date = datetime.fromisoformat(data.get("scheduled_date"))
            print(f"Parsed scheduled date: {scheduled_publish_date}")
            # If the scheduled date is in the future, mark as unpublished
            if scheduled_publish_date > datetime.now():
                is_published = False
                is_draft = False  # Make sure it's not marked as a draft
                is_scheduled = True  # Mark as scheduled
                print("Post will be scheduled (not published immediately)")
            else:
                print("Scheduled date is in the past, publishing immediately")
        except ValueError as e:
            # If date parsing fails, ignore scheduling
            print(f"Error parsing scheduled date: {e}")
            pass

    # Create new post with error handling
    try:
        print("Creating new post via API...")
        print(f"User ID: {user_id}")
        print(f"Is Published: {is_published}")
        print(f"Is Draft: {is_draft}")
        print(f"Scheduled Publish Date: {scheduled_publish_date}")

        # Try a simpler approach - create a dictionary of values first
        post_data = {
            "title": title,
            "content": clean_content,
            "user_id": user_id,
            "is_featured": False,
            "category": data.get("category"),
            "scheduled_publish_date": scheduled_publish_date,
            "is_published": is_published,
            "is_scheduled": is_scheduled,
            # Don't include updated_at to let the model handle it
            "created_at": datetime.now()
        }

        # Try to add is_draft if it exists in the model
        try:
            # Check if BlogPost has is_draft attribute
            if hasattr(BlogPost, 'is_draft'):
                post_data["is_draft"] = is_draft
                print("Added is_draft to post data")
        except Exception as e:
            print(f"Error checking for is_draft attribute: {e}")

        # Create the post object
        try:
            print("Creating BlogPost object with data:")
            for key, value in post_data.items():
                print(f"  {key}: {value}")

            # Try to create the post using raw SQL to avoid ORM issues
            from sqlalchemy import text

            # Build the column names and placeholders
            columns = list(post_data.keys())
            placeholders = [f":{col}" for col in columns]

            # Create the SQL query
            query = text(f"""
            INSERT INTO blog_posts ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            """)

            # Execute the query
            print(f"Executing SQL query: {query}")
            result = await db.execute(query, post_data)
            await db.commit()

            # Get the last inserted ID
            get_last_id_query = text("SELECT last_insert_rowid()")
            last_id_result = await db.execute(get_last_id_query)
            post_id = last_id_result.scalar()

            # Get the post object
            get_post_query = text("SELECT * FROM blog_posts WHERE id = :id")
            post_result = await db.execute(get_post_query, {"id": post_id})
            post = post_result.fetchone()

            print(f"Post created successfully with ID: {post_id}")
        except Exception as e:
            print(f"Error creating blog post: {e}")
            try:
                if db and hasattr(db, 'is_active') and db.is_active:
                    await db.rollback()
                else:
                    print("No active database session to rollback")
            except Exception as rollback_error:
                print(f"Error during rollback: {rollback_error}")
            return {"success": False, "error": f"Error creating post object: {str(e)}"}

        # We've already committed the post in the previous step
        print(f"Successfully created post: {title}")
    except Exception as e:
        print(f"Unexpected error creating post: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": f"Unexpected error: {str(e)}"}

    # Format created_at for JSON response
    created_at = post.created_at.isoformat() if post.created_at else None

    # Determine the appropriate redirect URL and set active tab
    redirect_url = "/author-dashboard"
    active_tab = "posts"

    if is_scheduled:
        active_tab = "scheduled"
        redirect_url = "/author-dashboard#scheduled"
    elif is_draft:
        active_tab = "drafts"
        redirect_url = "/author-dashboard#drafts"
    elif is_published:
        redirect_url = "/"

    # Try to set the active tab in the session
    try:
        request.session["active_tab"] = active_tab
        print(f"API: Setting active_tab to '{active_tab}' in session")
    except Exception as e:
        print(f"API: Could not set active tab in session: {e}")

    return {
        "success": True,
        "redirect": redirect_url,
        "post": {
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "created_at": created_at,
            "user_id": user_id,
            "is_featured": post.is_featured,
            "category": post.category,
            "scheduled_publish_date": post.scheduled_publish_date.isoformat() if post.scheduled_publish_date else None,
            "is_published": post.is_published
        }
    }

# Category view route
@router.get("/category/{category}")
async def category(category: str, request: Request, db: AsyncSession = Depends(get_db)):
    """Filter and display posts by category."""
    # Get user info
    user = request.state.user
    is_authenticated = user is not None

    # Get posts for this category
    try:
        query = select(BlogPost, User).outerjoin(User, BlogPost.user_id == User.id).where(
            (BlogPost.is_published == True) &
            (BlogPost.is_draft == False) &
            (func.lower(BlogPost.category) == func.lower(category))
        ).order_by(BlogPost.created_at.desc())

        result = await db.execute(query)
        post_users = result.fetchall()
    except Exception as e:
        print(f"Error fetching posts for category {category}: {e}")
        post_users = []

    # Format posts for template
    posts = []
    for post, author in post_users:
        posts.append({
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "created_at": post.created_at,
            "user_id": post.user_id,
            "category": post.category,
            "author": author,
            "image": getattr(post, "image", "/static/images/default-post.jpg"),
            "excerpt": post.content[:200] + "..." if len(post.content) > 200 else post.content
        })

    # Get all categories for sidebar
    try:
        # Get distinct categories from published posts
        category_query = select(func.distinct(BlogPost.category)).where(
            (BlogPost.is_published == True) &
            (BlogPost.is_draft == False) &
            (BlogPost.category != None)  # Exclude posts without categories
        )
        category_result = await db.execute(category_query)
        categories = [cat[0] for cat in category_result if cat[0]]  # Filter out None values
    except Exception as e:
        print(f"Error fetching categories: {e}")
        categories = []

    return templates.TemplateResponse(
        "blog.html",
        {
            "request": request,
            "posts": posts,
            "featured_posts": [],  # No featured posts in category view
            "related_posts": [],  # No related posts in category view
            "categories": categories,
            "current_category": category,
            "user": user,
            "is_authenticated": is_authenticated
        }
    )

# Single post view route
@router.get("/post/{post_id}")
async def view_post(post_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    # Get user info
    user = request.state.user
    is_authenticated = user is not None

    # Get the post with author information
    try:
        query = select(BlogPost, User).outerjoin(User, BlogPost.user_id == User.id).where(BlogPost.id == post_id)
        print(f"View post query: {str(query)}")
        result = await db.execute(query)
        post_author = result.first()
    except Exception as e:
        print(f"Error fetching post: {e}")
        post_author = None

    if not post_author:
        # Post not found, redirect to blog home
        return RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)

    post, author = post_author

    # Format post for template
    post_data = {
        "id": post.id,
        "title": post.title,
        "content": post.content,
        "created_at": post.created_at,
        "user_id": post.user_id,
        "category": post.category,
        "is_featured": post.is_featured,
        "author": author
    }

    return templates.TemplateResponse(
        "post_detail.html",
        {
            "request": request,
            "post": post_data,
            "user": user,
            "is_authenticated": is_authenticated
        }
    )

# The home route has been merged with the read_blog route above

@router.get("/search")
async def search_posts(request: Request, q: str = "", db: AsyncSession = Depends(get_db)):
    # Get user info
    user = request.state.user
    is_authenticated = user is not None

    # Search posts by title or content
    search_term = f"%{q}%"
    query = select(BlogPost, User).join(User, BlogPost.user_id == User.id).where(
        (func.lower(BlogPost.title).like(func.lower(search_term))) |
        (func.lower(BlogPost.content).like(func.lower(search_term)))
    ).order_by(BlogPost.created_at.desc())

    result = await db.execute(query)
    post_users = result.fetchall()

    posts = []
    for post, author in post_users:
        posts.append({
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "created_at": post.created_at,
            "author": {
                "id": author.id,
                "username": author.username
            }
        })

    return templates.TemplateResponse(
        "search_results.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": is_authenticated,
            "posts": posts,
            "search_query": q,
            "result_count": len(posts)
        }
    )

# API endpoints for blog comments
@router.post("/api/posts/{post_id}/comments")
async def add_comment(post_id: int, request: Request, data: Dict[str, Any] = Body(...), db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Get the post
    try:
        query = select(BlogPost).where(BlogPost.id == post_id)
        print(f"Add comment - get post query: {str(query)}")
        result = await db.execute(query)
        post = result.scalars().first()
    except Exception as e:
        print(f"Error fetching post for comment: {e}")
        raise HTTPException(status_code=500, detail="Error fetching post")

    # Check if post exists
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    # Get comment content
    content = data.get("content")
    if not content or not content.strip():
        raise HTTPException(status_code=400, detail="Comment content is required")

    # Check if this is a reply to another comment
    parent_id = data.get("parent_id")

    # If parent_id is provided, verify the parent comment exists
    if parent_id:
        try:
            parent_query = select(BlogComment).where(BlogComment.id == parent_id, BlogComment.post_id == post_id)
            print(f"Parent comment query: {str(parent_query)}")
            parent_result = await db.execute(parent_query)
            parent_comment = parent_result.scalars().first()

            if not parent_comment:
                raise HTTPException(status_code=404, detail="Parent comment not found")
        except Exception as e:
            print(f"Error fetching parent comment: {e}")
            raise HTTPException(status_code=500, detail="Error fetching parent comment")

    # Check for prohibited content
    content_allowed, content_violations = check_content(content)
    if not content_allowed:
        error_message = get_violation_message(content_violations)
        return {"success": False, "error": error_message}

    # Create comment
    comment = BlogComment(
        post_id=post_id,
        user_id=user.id,
        content=content,
        parent_id=parent_id
    )

    db.add(comment)
    await db.commit()
    await db.refresh(comment)

    # Return the comment data
    return {
        "success": True,
        "comment": {
            "id": comment.id,
            "content": comment.content,
            "created_at": comment.created_at.isoformat(),
            "author": {
                "id": user.id,
                "username": user.username,
                "profile_picture": user.profile_picture
            }
        }
    }

@router.get("/api/posts/{post_id}/comments")
async def get_comments(post_id: int, request: Request, db: AsyncSession = Depends(get_db)):
    # Get the post
    post_query = select(BlogPost).where(BlogPost.id == post_id)
    post_result = await db.execute(post_query)
    post = post_result.scalars().first()

    # Check if post exists
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    # Get comments for the post
    try:
        # Use a raw SQL query with the safe execution function
        safe_query = """
        SELECT bc.id, bc.content, bc.created_at, bc.parent_id, bc.post_id, bc.user_id,
               u.id as user_id, u.username, u.profile_picture
        FROM blog_comments bc
        JOIN users u ON bc.user_id = u.id
        WHERE bc.post_id = :post_id
        ORDER BY bc.created_at
        """

        print(f"Safe comments query: {safe_query}")
        comments_result = await execute_safe_query(db, safe_query, {"post_id": post_id})

        # Convert the result to a list of tuples (comment, user)
        comment_users = []
        for row in comments_result:
            # Create a simple object to represent the comment
            comment = SimpleNamespace(
                id=row.id,
                content=row.content,
                created_at=row.created_at,
                parent_id=row.parent_id,
                post_id=row.post_id,
                user_id=row.user_id
            )

            # Create a simple object to represent the user
            user = SimpleNamespace(
                id=row.user_id,
                username=row.username,
                profile_picture=row.profile_picture
            )

            comment_users.append((comment, user))
    except Exception as e:
        print(f"Error fetching comments: {e}")
        comment_users = []

    # Format comments for response
    comments_dict = {}
    comment_tree = []

    # First pass: create a dictionary of all comments
    for comment, user in comment_users:
        comment_data = {
            "id": comment.id,
            "content": comment.content,
            "created_at": comment.created_at.isoformat(),
            "parent_id": comment.parent_id,
            "author": {
                "id": user.id,
                "username": user.username,
                "profile_picture": user.profile_picture
            },
            "replies": []
        }
        comments_dict[comment.id] = comment_data

    # Second pass: build the comment tree
    for comment_id, comment_data in comments_dict.items():
        # If it's a reply, add it to its parent's replies
        if comment_data["parent_id"]:
            parent_comment = comments_dict.get(comment_data["parent_id"])
            if parent_comment:
                parent_comment["replies"].append(comment_data)
        # If it's a top-level comment, add it to the result list
        else:
            comment_tree.append(comment_data)

    return {"success": True, "comments": comment_tree}

# API endpoint for content reports
@router.post("/api/report-content")
async def report_content(request: Request, data: Dict[str, Any] = Body(...), db: AsyncSession = Depends(get_db)):
    # Check if user is logged in
    user = request.state.user
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required")

    # Get report data
    content_type = data.get("content_type")
    content_id = data.get("content_id")
    reason = data.get("reason")
    details = data.get("details")

    # Validate input
    if not content_type or not content_id or not reason or not details:
        raise HTTPException(status_code=400, detail="All fields are required")

    # Create report
    report = ContentReport(
        content_type=content_type,
        content_id=content_id,
        user_id=user.id,
        reason=reason,
        details=details
    )

    db.add(report)
    await db.commit()
    await db.refresh(report)

    return {"success": True, "message": "Thank you for your report. Our team will review it shortly."}