"""
Statement of Faith routes
"""

from fastapi import APIRouter, Request
from ..template_config import templates

router = APIRouter()

@router.get("/statement-of-faith")
async def statement_of_faith(request: Request):
    """
    Statement of Faith page
    """
    # Get user info
    user = request.state.user
    is_authenticated = user is not None
    
    return templates.TemplateResponse(
        "statement_of_faith.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": is_authenticated
        }
    )
