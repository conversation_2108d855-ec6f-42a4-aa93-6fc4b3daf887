from fastapi import APIRouter, Depends, HTTPException, status, Request, Response, Form, File, UploadFile, Body
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import timedelta
import json
import secrets
import uuid
from typing import Dict, Any, Optional
from ..database import get_db
from ..models import User
from ..template_config import templates
from ..auth import (
    authenticate_user,
    create_access_token,
    get_password_hash,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    get_current_user,
    get_user_by_email,
    create_password_reset_token,
    verify_password_reset_token,
    save_profile_picture,
    update_user_profile
)
from ..webauthn import (
    get_registration_options,
    verify_registration,
    get_authentication_options,
    verify_authentication,
    get_user_by_username
)
from ..redis_store import (
    store_webauthn_challenge,
    get_webauthn_challenge,
    delete_webauthn_challenge
)
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

router = APIRouter()

@router.get("/login")
async def login_page(request: Request, next: str = None):
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "next": next}
    )

@router.get("/register")
async def register_page(request: Request):
    return templates.TemplateResponse("register.html", {"request": request})

@router.post("/login")
async def login(
    request: Request,
    response: Response,
    username: str = Form(...),
    password: str = Form(...),
    next: str = Form(None),
    remember_me: bool = Form(False),
    db: AsyncSession = Depends(get_db)
):
    user = await authenticate_user(db, username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Incorrect username or password", "next": next},
            status_code=status.HTTP_401_UNAUTHORIZED
        )

    # Set token expiration based on remember_me option
    if remember_me:
        # 30 days in minutes
        access_token_expires = timedelta(days=30)
        cookie_max_age = 30 * 24 * 60 * 60  # 30 days in seconds
    else:
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        cookie_max_age = ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Convert minutes to seconds

    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Redirect to the next URL if provided, otherwise go to home page
    redirect_url = next if next else "/"
    response = RedirectResponse(url=redirect_url, status_code=status.HTTP_302_FOUND)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=cookie_max_age,
        expires=cookie_max_age,
        secure=request.url.scheme == "https"  # Set secure flag if using HTTPS
    )
    return response

@router.post("/register")
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    phone_number: str = Form(None),
    password: str = Form(...),
    confirm_password: str = Form(...),
    db: AsyncSession = Depends(get_db)
):
    try:
        # Validate passwords
        if password != confirm_password:
            return templates.TemplateResponse(
                "register.html",
                {"request": request, "error": "Passwords do not match"},
                status_code=status.HTTP_400_BAD_REQUEST
            )

        if len(password) < 8:
            return templates.TemplateResponse(
                "register.html",
                {"request": request, "error": "Password must be at least 8 characters long"},
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Check username
        result = await db.execute(select(User).where(User.username == username))
        if result.scalars().first():
            return templates.TemplateResponse(
                "register.html",
                {"request": request, "error": "Username already registered"},
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Check email
        result = await db.execute(select(User).where(User.email == email))
        if result.scalars().first():
            return templates.TemplateResponse(
                "register.html",
                {"request": request, "error": "Email already registered"},
                status_code=status.HTTP_400_BAD_REQUEST
            )

        # Check phone number
        if phone_number:
            result = await db.execute(select(User).where(User.phone_number == phone_number))
            if result.scalars().first():
                return templates.TemplateResponse(
                    "register.html",
                    {"request": request, "error": "Phone number already registered"},
                    status_code=status.HTTP_400_BAD_REQUEST
                )

        # Create user
        hashed_password = get_password_hash(password)
        user = User(
            username=username,
            email=email,
            phone_number=phone_number,
            hashed_password=hashed_password,
            role="user"
        )
        db.add(user)
        await db.commit()
        await db.refresh(user)

        # Create access token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username}, 
            expires_delta=access_token_expires
        )

        # Create response with redirect template
        response = templates.TemplateResponse(
            "register.html",
            {
                "request": request,
                "success": "Registration successful! Please wait while we redirect you...",
                "redirect": "/create-profile"
            }
        )
        
        # Set the authentication cookie
        response.set_cookie(
            key="access_token",
            value=f"Bearer {access_token}",
            httponly=True,
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            secure=request.url.scheme == "https",
            samesite="lax"
        )
        
        return response

    except IntegrityError as e:
        print(f"IntegrityError during registration: {e}")
        await db.rollback()
        # Try to parse which field caused the error
        error_message = str(e.orig)
        if 'username' in error_message:
            user_error = "Username already exists. Please choose another."
        elif 'email' in error_message:
            user_error = "Email already exists. Please use another."
        elif 'phone_number' in error_message:
            user_error = "Phone number already exists. Please use another."
        else:
            user_error = "A unique constraint was violated. Please check your input."
        return templates.TemplateResponse(
            "register.html",
            {"request": request, "error": user_error},
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        print(f"Error during registration: {e}")
        try:
            if db is not None:
                await db.rollback()
            else:
                print("No active database session to rollback")
        except Exception as rollback_error:
            print(f"Error during rollback: {rollback_error}")
        return templates.TemplateResponse(
            "register.html",
            {"request": request, "error": "An error occurred during registration. Please try again."},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@router.get("/logout")
async def logout(response: Response):
    response = RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)
    response.delete_cookie(key="access_token")
    return response

@router.get("/token")
async def get_token(request: Request):
    token = request.cookies.get("access_token")
    if not token:
        return {"authenticated": False}
    return {"authenticated": True}

# WebAuthn routes
@router.post("/webauthn/register/options")
async def webauthn_register_options(
    request: Request,
    username: str = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """Get WebAuthn registration options for a user."""
    try:
        # Get the user
        user = await get_user_by_username(db, username)
        if not user:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={"detail": "User not found"}
            )

        # Generate registration options
        options = await get_registration_options(db, user, request)

        return JSONResponse(content=options)
    except Exception as e:
        print(f"Error generating WebAuthn registration options: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": f"Error generating registration options: {str(e)}"}
        )

@router.post("/webauthn/register/verify")
async def webauthn_register_verify(
    request: Request,
    username: str = Body(...),
    credential: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """Verify a WebAuthn registration response."""
    try:
        # Get the user
        user = await get_user_by_username(db, username)
        if not user:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={"detail": "User not found"}
            )

        # Verify the registration
        result = await verify_registration(db, user, credential, request)
        if not result:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"detail": "Registration failed"}
            )

        # Create a session for the user
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )

        # Create a response with the token in a cookie
        response = JSONResponse(content={"success": True, "message": "Registration successful"})
        response.set_cookie(
            key="access_token",
            value=f"Bearer {access_token}",
            httponly=True,
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            expires=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        )

        return response
    except Exception as e:
        print(f"Error verifying WebAuthn registration: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": f"Error verifying registration: {str(e)}"}
        )

@router.post("/webauthn/login/options")
async def webauthn_login_options(
    request: Request,
    username: str = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """Get WebAuthn authentication options for a user."""
    try:
        # Get the user
        user = await get_user_by_username(db, username)
        if not user:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={"detail": "User not found"}
            )

        # Check if the user has WebAuthn credentials
        if not user.webauthn_credentials:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"detail": "No WebAuthn credentials found for this user"}
            )

        # Generate authentication options
        options = await get_authentication_options(db, user, request)

        return JSONResponse(content=options)
    except Exception as e:
        print(f"Error generating WebAuthn options: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": f"Error generating WebAuthn options: {str(e)}"}
        )

@router.post("/webauthn/login/verify")
async def webauthn_login_verify(
    request: Request,
    username: str = Body(...),
    credential: Dict[str, Any] = Body(...),
    remember_me: bool = Body(False),
    db: AsyncSession = Depends(get_db)
):
    """Verify a WebAuthn authentication response."""
    try:
        # Get the user
        user = await get_user_by_username(db, username)
        if not user:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={"detail": "User not found"}
            )

        # Check if the user has WebAuthn credentials
        if not user.webauthn_credentials:
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"detail": "No WebAuthn credentials found for this user"}
            )

        # Verify the authentication
        result = await verify_authentication(db, user, credential, request)
        if not result:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Authentication failed"}
            )

        # Set token expiration based on remember_me option
        if remember_me:
            # 30 days in minutes
            access_token_expires = timedelta(days=30)
            cookie_max_age = 30 * 24 * 60 * 60  # 30 days in seconds
        else:
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            cookie_max_age = ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Convert minutes to seconds

        # Create a session for the user
        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )

        # Create a response with the token in a cookie
        response = JSONResponse(content={"success": True, "message": "Authentication successful"})
        response.set_cookie(
            key="access_token",
            value=f"Bearer {access_token}",
            httponly=True,
            max_age=cookie_max_age,
            expires=cookie_max_age,
        )

        return response
    except Exception as e:
        print(f"Error verifying WebAuthn authentication: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": f"Error verifying authentication: {str(e)}"}
        )

# Forgot password routes
@router.get("/forgot-password")
async def forgot_password_page(request: Request):
    return templates.TemplateResponse("forgot_password.html", {"request": request})

@router.post("/forgot-password")
async def forgot_password(
    request: Request,
    email: str = Form(...),
    db: AsyncSession = Depends(get_db)
):
    user = await get_user_by_email(db, email)
    if not user:
        return templates.TemplateResponse(
            "forgot_password.html",
            {"request": request, "error": "Email not found"}
        )

    # Generate reset token
    token = create_password_reset_token(email)

    # In a real application, you would send an email with the reset link
    # For this example, we'll just show the token in the success message
    reset_link = f"/reset-password/{token}"

    return templates.TemplateResponse(
        "forgot_password.html",
        {
            "request": request,
            "success": f"Password reset link has been sent to your email. For demo purposes, here's the link: {reset_link}"
        }
    )

@router.get("/reset-password/{token}")
async def reset_password_page(request: Request, token: str, db: AsyncSession = Depends(get_db)):
    user = await verify_password_reset_token(token, db)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid or expired reset token"}
        )

    return templates.TemplateResponse("reset_password.html", {"request": request, "token": token})

@router.post("/reset-password/{token}")
async def reset_password(
    request: Request,
    token: str,
    password: str = Form(...),
    confirm_password: str = Form(...),
    db: AsyncSession = Depends(get_db)
):
    if password != confirm_password:
        return templates.TemplateResponse(
            "reset_password.html",
            {"request": request, "token": token, "error": "Passwords do not match"}
        )

    user = await verify_password_reset_token(token, db)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid or expired reset token"}
        )

    # Update password
    hashed_password = get_password_hash(password)
    user.hashed_password = hashed_password
    await db.commit()

    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Password has been reset successfully. Please login."}
    )

# Profile routes
@router.get("/create-profile")
async def create_profile_page(request: Request, db: AsyncSession = Depends(get_db)):
    user = request.state.user
    if not user:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    return templates.TemplateResponse("create_profile.html", {"request": request, "user": user})

@router.post("/create-profile")
async def create_profile(
    request: Request,
    full_name: str = Form(...),
    bio: str = Form(None),
    location: str = Form(None),
    website: str = Form(None),
    profile_picture: UploadFile = File(None),
    db: AsyncSession = Depends(get_db)
):
    user = request.state.user
    if not user:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    # Save profile picture if provided
    profile_picture_path = None
    if profile_picture and profile_picture.filename:
        profile_picture_path = await save_profile_picture(profile_picture)

    # Update user profile
    profile_data = {
        "full_name": full_name,
        "bio": bio,
        "location": location,
        "website": website,
        "has_completed_profile": True
    }

    if profile_picture_path:
        profile_data["profile_picture"] = profile_picture_path

    await update_user_profile(db, user.id, profile_data)

    # Redirect to create post page
    return RedirectResponse(url="/create-post", status_code=status.HTTP_302_FOUND)

# Dependency to get the current user from cookie
async def get_current_user_from_cookie(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    token = request.cookies.get("access_token")
    if not token:
        return None

    try:
        token = token.replace("Bearer ", "")
        user = await get_current_user(token, db)
        return user
    except:
        return None
