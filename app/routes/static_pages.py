"""
Static Page Routes
These routes handle static content pages like About, Contact, etc.
"""

from fastapi import APIRouter, Request
from ..template_config import templates

router = APIRouter()

@router.get("/about")
async def about_page(request: Request):
    """
    About Us page
    """
    # Get user info
    user = request.state.user
    is_authenticated = user is not None
    
    return templates.TemplateResponse(
        "about.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": is_authenticated
        }
    )

@router.get("/contact")
async def contact_page(request: Request):
    """
    Contact Us page
    """
    user = request.state.user
    is_authenticated = user is not None
    
    return templates.TemplateResponse(
        "contact.html",
        {
            "request": request,
            "user": user,
            "is_authenticated": is_authenticated
        }
    )
