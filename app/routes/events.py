from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime

from ..database import get_db
from ..models import Event

router = APIRouter()

@router.get("/api/events")
async def get_events(db: AsyncSession = Depends(get_db)):
    """Get all upcoming events"""
    try:
        events_query = select(Event).where(Event.event_date >= datetime.now()).order_by(Event.event_date.asc())
        result = await db.execute(events_query)
        events = result.scalars().all()
        
        return {
            "events": [
                {
                    "id": event.id,
                    "title": event.title,
                    "description": event.description,
                    "date": event.event_date.isoformat(),
                    "location": event.location,
                    "link": event.link
                }
                for event in events
            ]
        }
    except Exception as e:
        print(f"Error getting events: {e}")
        return {"events": []}
