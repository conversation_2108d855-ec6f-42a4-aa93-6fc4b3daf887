from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from typing import Generator
import os

# Database configuration for SQLite
DATABASE_URL = "sqlite:///davartruth.db"

# Create engine with SQLite optimizations
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False},  # Allow multiple threads to access SQLite
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for declarative models
Base = declarative_base()

# SQLite optimizations
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    cursor = dbapi_connection.cursor()
    # Enable WAL mode for better concurrent access
    cursor.execute("PRAGMA journal_mode=WAL")
    # Ensure foreign key constraints are enforced
    cursor.execute("PRAGMA foreign_keys=ON")
    # Set page size for better performance
    cursor.execute("PRAGMA page_size=4096")
    # Set cache size (in pages)
    cursor.execute("PRAGMA cache_size=-2000")  # About 8MB of cache
    # Enable memory-mapped I/O for better performance
    cursor.execute("PRAGMA mmap_size=8589934592")  # 8GB
    cursor.close()

def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class Pagination:
    def __init__(self, query, page: int = 1, per_page: int = 10):
        self.query = query
        self.page = page
        self.per_page = per_page
        self._total = None

    @property
    def total(self):
        if self._total is None:
            self._total = self.query.count()
        return self._total

    @property
    def items(self):
        return self.query.offset((self.page - 1) * self.per_page).limit(self.per_page).all()

    @property
    def pages(self):
        return max(1, (self.total + self.per_page - 1) // self.per_page)

    @property
    def has_next(self):
        return self.page < self.pages

    @property
    def has_prev(self):
        return self.page > 1
