"""
Optimized Static Files Handler for DavarTruth

This module provides an enhanced StaticFiles class that:
1. Adds proper MIME types for all file types
2. Adds Cache-Control headers based on file types
3. Supports HTTP range requests for better streaming
"""

from fastapi.staticfiles import StaticFiles
from starlette.responses import FileResponse, Response
from starlette.types import Scope, Receive, Send
from pathlib import Path
import mimetypes
import os
import time
import hashlib
from typing import Dict, List, Optional, Tuple, Union

# Add additional MIME types
mimetypes.add_type("application/javascript", ".js")
mimetypes.add_type("text/css", ".css")
mimetypes.add_type("image/webp", ".webp")
mimetypes.add_type("font/woff2", ".woff2")
mimetypes.add_type("font/woff", ".woff")
mimetypes.add_type("font/ttf", ".ttf")
mimetypes.add_type("application/manifest+json", ".webmanifest")
mimetypes.add_type("application/manifest+json", ".json")
mimetypes.add_type("text/html", ".html")

class OptimizedStaticFiles(StaticFiles):
    """
    Enhanced StaticFiles class with performance optimizations.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Cache rules based on file extensions
        self.cache_rules = {
            # Format: extension: (max_age_seconds, cache_control_directives)
            
            # Long-lived assets (1 year)
            "woff2": (31536000, "public, max-age=31536000, immutable"),
            "woff": (31536000, "public, max-age=31536000, immutable"),
            "ttf": (31536000, "public, max-age=31536000, immutable"),
            "eot": (31536000, "public, max-age=31536000, immutable"),
            
            # Medium-lived assets (1 week)
            "css": (604800, "public, max-age=604800"),
            "js": (604800, "public, max-age=604800"),
            "png": (604800, "public, max-age=604800"),
            "jpg": (604800, "public, max-age=604800"),
            "jpeg": (604800, "public, max-age=604800"),
            "gif": (604800, "public, max-age=604800"),
            "webp": (604800, "public, max-age=604800"),
            "svg": (604800, "public, max-age=604800"),
            "ico": (604800, "public, max-age=604800"),
            
            # Short-lived assets (1 day)
            "json": (86400, "public, max-age=86400"),
            "webmanifest": (86400, "public, max-age=86400"),
            
            # Special cases
            "sw.js": (3600, "public, max-age=3600"),  # Service worker (1 hour)
            "html": (0, "no-store, must-revalidate"),  # HTML (no caching)
        }
        
        self.cache_manifest = {}
        self.cdn_url = os.getenv('CDN_URL', '')
        self.generate_manifest()

    def generate_manifest(self):
        """Generate file hashes for cache busting"""
        for root, _, files in os.walk(self.directory):
            for file in files:
                filepath = os.path.join(root, file)
                with open(filepath, 'rb') as f:
                    file_hash = hashlib.md5(f.read()).hexdigest()[:8]
                rel_path = os.path.relpath(filepath, self.directory)
                self.cache_manifest[rel_path] = file_hash

    def get_cdn_url(self, path: str) -> str:
        """Get CDN URL for static file with cache busting"""
        if not self.cdn_url:
            return path
            
        file_hash = self.cache_manifest.get(path, '')
        cdn_path = f"{self.cdn_url}/{path}?v={file_hash}"
        return cdn_path
    
    async def get_response(self, path: str, scope: Scope) -> Response:
        """
        Get a response object for a file path, with optimized headers.
        """
        response = await super().get_response(path, scope)
        
        if isinstance(response, FileResponse):
            # Get file extension
            _, ext = os.path.splitext(path)
            ext = ext.lstrip(".").lower()
            
            # Special case for service worker
            if path.endswith("sw.js"):
                ext = "sw.js"
            
            # Apply cache rules based on extension
            if ext in self.cache_rules:
                max_age, cache_control = self.cache_rules[ext]
                
                # Set Cache-Control header
                response.headers["Cache-Control"] = cache_control
                
                # Add Expires header for HTTP/1.0 compatibility
                if max_age > 0:
                    expires = time.strftime(
                        "%a, %d %b %Y %H:%M:%S GMT", 
                        time.gmtime(time.time() + max_age)
                    )
                    response.headers["Expires"] = expires
                
                # Add Vary header for proper caching
                response.headers["Vary"] = "Accept-Encoding"
            
            # Ensure proper content type
            content_type, _ = mimetypes.guess_type(path)
            if content_type:
                response.headers["Content-Type"] = content_type
        
        return response

# Content Security Policy
CSP_DIRECTIVES = {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", 'cdn.jsdelivr.net'],
    'style-src': ["'self'", "'unsafe-inline'", 'fonts.googleapis.com'],
    'img-src': ["'self'", 'data:', '*'],
    'font-src': ["'self'", 'fonts.gstatic.com'],
    'connect-src': ["'self'"],
}
