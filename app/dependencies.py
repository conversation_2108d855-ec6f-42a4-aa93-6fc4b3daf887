from fastapi import Depends, HTTPException, status, Request
from jose import JW<PERSON>rror, jwt
from typing import Optional
from app.database import get_async_db
from app.models import User
from app.auth import oauth2_scheme, SECRET_KEY, ALGORITHM
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_async_db)
) -> Optional[User]:
    """Get the current user from the JWT token."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    query = select(User).where(User.username == username)
    result = await db.execute(query)
    user = result.scalar_one_or_none()
    
    if user is None:
        raise credentials_exception
    return user

async def get_current_user_from_cookie(
    request: Request,
    db: AsyncSession = Depends(get_async_db)
) -> Optional[User]:
    """Get the current user from the session cookie."""
    token = request.cookies.get("access_token")
    if not token:
        return None

    try:
        token = token.replace("Bearer ", "")
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
            
        query = select(User).where(User.username == username)
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        return user
    except Exception:
        return None
