"""
Error handlers for the DavarTruth application.
"""

import logging
import traceback
from fastapi import Request, status
from fastapi.responses import JSONResponse, HTMLResponse, PlainTextResponse
from jinja2.exceptions import TemplateError

logger = logging.getLogger(__name__)

async def template_exception_handler(request: Request, exc: TemplateError):
    """
    Handle template rendering errors and provide detailed error information.
    """
    error_detail = str(exc)
    tb = traceback.format_exc()

    # Log the error with traceback
    logger.error(f"Template error: {error_detail}")
    logger.error(f"Traceback: {tb}")

    # Check if the request accepts HTML
    accept_header = request.headers.get("accept", "")

    if "text/html" in accept_header:
        # For browser requests, return a user-friendly HTML error page
        error_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Template Error</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }}
                h1 {{ color: #d32f2f; }}
                .error-box {{ background-color: #ffebee; border-left: 5px solid #d32f2f; padding: 15px; margin-bottom: 20px; }}
                .traceback {{ background-color: #f5f5f5; padding: 15px; overflow-x: auto; font-family: monospace; white-space: pre-wrap; }}
                .actions {{ margin-top: 30px; }}
                .actions a {{ display: inline-block; margin-right: 10px; padding: 8px 16px; background-color: #5c0e14; color: white; text-decoration: none; border-radius: 4px; }}
                .actions a:hover {{ background-color: #4a0b10; }}
            </style>
        </head>
        <body>
            <h1>Template Error</h1>
            <div class="error-box">
                <p><strong>Error:</strong> {error_detail}</p>
            </div>

            <h2>Traceback</h2>
            <div class="traceback">{tb}</div>

            <div class="actions">
                <a href="javascript:history.back()">Go Back</a>
                <a href="/">Go to Home</a>
            </div>
        </body>
        </html>
        """
        # Create response with proper Content-Length header
        response = HTMLResponse(content=error_content, status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        response.headers["Content-Length"] = str(len(error_content.encode("utf-8")))
        return response

    elif "application/json" in accept_header:
        # For API requests, return a JSON error response
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "Template rendering error", "error": error_detail}
        )

    else:
        # For other requests, return a plain text error response
        return PlainTextResponse(
            f"Template rendering error: {error_detail}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def setup_error_handlers(app):
    """
    Register all error handlers with the FastAPI application.
    """
    from jinja2.exceptions import TemplateError

    # Register the template error handler
    app.add_exception_handler(TemplateError, template_exception_handler)

    # Add more error handlers as needed
