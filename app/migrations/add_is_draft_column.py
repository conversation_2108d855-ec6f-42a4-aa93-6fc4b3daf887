from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from ..database import get_async_db

async def run_migration():
    """
    Add is_draft column to blog_posts table if it doesn't exist
    """
    async with get_async_db() as db:
        try:
            # Check if the column already exists
            check_query = text("PRAGMA table_info(blog_posts)")
            result = await db.execute(check_query)
            columns = result.fetchall()
            
            # Check if is_draft column exists
            is_draft_exists = any(column.name == 'is_draft' for column in columns)
            
            if not is_draft_exists:
                print("Adding is_draft column to blog_posts table...")
                # Add the column with a default value of 0 (False)
                add_column_query = text("ALTER TABLE blog_posts ADD COLUMN is_draft BOOLEAN DEFAULT 0")
                await db.execute(add_column_query)
                
                # Set initial values - drafts are unpublished posts
                await db.execute(
                    text("UPDATE blog_posts SET is_draft = TRUE WHERE is_published = FALSE")
                )
                
                await db.commit()
                print("Successfully added is_draft column to blog_posts table")
            else:
                print("is_draft column already exists in blog_posts table")
                
            return True
        except Exception as e:
            print(f"Error in migration: {e}")
            await db.rollback()
            import traceback
            traceback.print_exc()
            return False
