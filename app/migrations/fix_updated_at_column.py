from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from ..database import get_async_db
import traceback

async def run_migration():
    """
    Fix the updated_at column in blog_posts table
    """
    async with get_async_db() as db:
        try:
            print("Checking blog_posts table schema...")
            
            # First, check if the table exists
            check_table_query = text("SELECT name FROM sqlite_master WHERE type='table' AND name='blog_posts'")
            result = await db.execute(check_table_query)
            tables = result.fetchall()
            
            if not tables:
                print("blog_posts table does not exist!")
                return False
                
            # Check if the column already exists
            check_query = text("PRAGMA table_info(blog_posts)")
            result = await db.execute(check_query)
            columns = result.fetchall()
            
            # Print all columns for debugging
            print("Current blog_posts columns:")
            for column in columns:
                print(f"  {column}")
            
            # Check if updated_at column exists
            updated_at_exists = any(column.name == 'updated_at' for column in columns)
            
            if not updated_at_exists:
                print("updated_at column does not exist in blog_posts table. Adding it...")
                
                try:
                    # Try to add the column
                    add_column_query = text("ALTER TABLE blog_posts ADD COLUMN updated_at TIMESTAMP")
                    await db.execute(add_column_query)
                    await db.commit()
                    print("Successfully added updated_at column to blog_posts table")
                except Exception as e:
                    print(f"Error adding updated_at column: {e}")
                    traceback.print_exc()
                    
                    # Try an alternative approach - create a new table with the correct schema
                    print("Trying alternative approach...")
                    
                    # Create a temporary table with the correct schema
                    create_temp_table_query = text("""
                    CREATE TABLE blog_posts_new (
                        id INTEGER PRIMARY KEY,
                        title VARCHAR(100),
                        content TEXT,
                        created_at TIMESTAMP,
                        updated_at TIMESTAMP,
                        user_id INTEGER,
                        is_featured BOOLEAN,
                        category VARCHAR(50),
                        scheduled_publish_date TIMESTAMP,
                        is_published BOOLEAN,
                        is_draft BOOLEAN,
                        FOREIGN KEY(user_id) REFERENCES users(id)
                    )
                    """)
                    
                    try:
                        await db.execute(create_temp_table_query)
                        
                        # Copy data from the old table to the new table
                        copy_data_query = text("""
                        INSERT INTO blog_posts_new (id, title, content, created_at, user_id, is_featured, category, scheduled_publish_date, is_published, is_draft)
                        SELECT id, title, content, created_at, user_id, is_featured, category, scheduled_publish_date, is_published, is_draft FROM blog_posts
                        """)
                        await db.execute(copy_data_query)
                        
                        # Drop the old table
                        drop_old_table_query = text("DROP TABLE blog_posts")
                        await db.execute(drop_old_table_query)
                        
                        # Rename the new table to the original name
                        rename_table_query = text("ALTER TABLE blog_posts_new RENAME TO blog_posts")
                        await db.execute(rename_table_query)
                        
                        await db.commit()
                        print("Successfully recreated blog_posts table with updated_at column")
                    except Exception as e2:
                        print(f"Error recreating table: {e2}")
                        traceback.print_exc()
                        await db.rollback()
                        return False
            else:
                print("updated_at column already exists in blog_posts table")
                
            # Update all NULL updated_at values to match created_at
            await db.execute(
                text("UPDATE blog_posts SET updated_at = created_at WHERE updated_at IS NULL")
            )
            
            # Index for faster querying
            await db.execute(
                text("CREATE INDEX IF NOT EXISTS idx_blog_posts_updated_at ON blog_posts(updated_at)")
            )
            
            await db.commit()
            print("Migration successful: fixed updated_at column")
                
        except Exception as e:
            print(f"Error in migration: {e}")
            traceback.print_exc()
            await db.rollback()
