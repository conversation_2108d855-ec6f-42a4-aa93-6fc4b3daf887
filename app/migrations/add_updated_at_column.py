from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from ..database import get_async_db

async def run_migration():
    """
    Add updated_at column to blog_posts table if it doesn't exist
    """
    async with get_async_db() as db:
        try:
            # Check if the column already exists
            check_query = text("PRAGMA table_info(blog_posts)")
            result = await db.execute(check_query)
            columns = result.fetchall()
            
            # Check if updated_at column exists
            updated_at_exists = any(column.name == 'updated_at' for column in columns)
            
            if not updated_at_exists:
                print("Adding updated_at column to blog_posts table...")
                # Add the column with a default value of current timestamp
                add_column_query = text("ALTER TABLE blog_posts ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
                await db.execute(add_column_query)
                
                # Set initial values
                await db.execute(
                    text("UPDATE blog_posts SET updated_at = created_at WHERE updated_at IS NULL")
                )
                
                await db.commit()
                print("Successfully added updated_at column to blog_posts table")
            else:
                print("updated_at column already exists in blog_posts table")
                
            return True
        except Exception as e:
            print(f"Error in migration: {e}")
            await db.rollback()
            import traceback
            traceback.print_exc()
            return False
