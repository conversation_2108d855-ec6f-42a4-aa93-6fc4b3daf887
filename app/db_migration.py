"""
Database migration script to update the schema when changes are made to models.py
"""

import asyncio
import sqlite3
from pathlib import Path
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from .database import get_async_db, engine

async def migrate_database():
    """
    Run database migrations to update the schema
    """
    print("Starting database migration...")

    # Get the database file path
    db_file = Path("davartruth.db")

    # Check if the database file exists
    if not db_file.exists():
        print("Database file not found. Creating new database.")
        # Create an empty database file
        open(db_file, 'a').close()
        print(f"Created empty database file at {db_file.absolute()}")

    # Connect to the database directly with sqlite3
    conn = sqlite3.connect("davartruth.db")
    cursor = conn.cursor()

    # Check if the users table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    if not cursor.fetchone():
        print("Creating users table...")
        cursor.execute("""
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            phone_number VARCHAR(20) UNIQUE,
            hashed_password VARCHAR(100) NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            role VARCHAR(20) DEFAULT 'user',
            is_banned BOOLEAN DEFAULT 0,
            ban_reason TEXT,
            full_name VARCHAR(100),
            bio TEXT,
            profile_picture VARCHAR(255),
            location VARCHAR(100),
            website VARCHAR(255),
            has_completed_profile BOOLEAN DEFAULT 0
        )
        """)

    # Check if the chat_rooms table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='chat_rooms'")
    if not cursor.fetchone():
        print("Creating chat_rooms table...")
        cursor.execute("""
        CREATE TABLE chat_rooms (
            id INTEGER PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            room_type VARCHAR(50) DEFAULT 'general',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            is_private BOOLEAN DEFAULT 0,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """)

        # Create default chat rooms
        cursor.execute("""
        INSERT INTO chat_rooms (name, description, room_type)
        VALUES
            ('General', 'General chat room', 'general'),
            ('Prayer Requests', 'Share and respond to prayer requests', 'prayer'),
            ('Bible Study', 'Discuss scripture and share insights', 'study')
        """)

    # Check if the chat_messages table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='chat_messages'")
    if not cursor.fetchone():
        print("Creating chat_messages table...")
        cursor.execute("""
        CREATE TABLE chat_messages (
            id INTEGER PRIMARY KEY,
            content TEXT,
            username VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER,
            room_id INTEGER,
            status VARCHAR(20) DEFAULT 'active',
            parent_id INTEGER,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (room_id) REFERENCES chat_rooms (id),
            FOREIGN KEY (parent_id) REFERENCES chat_messages (id)
        )
        """)
    else:
        # Check if the chat_messages table has the room_id column
        try:
            cursor.execute("SELECT room_id FROM chat_messages LIMIT 1")
        except sqlite3.OperationalError:
            print("Adding room_id column to chat_messages table...")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN room_id INTEGER")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN status VARCHAR(20) DEFAULT 'active'")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN parent_id INTEGER")

            # Set all existing messages to room_id 1 (General)
            cursor.execute("UPDATE chat_messages SET room_id = 1")

    # Check if the user_room_memberships table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user_room_memberships'")
    if not cursor.fetchone():
        print("Creating user_room_memberships table...")
        cursor.execute("""
        CREATE TABLE user_room_memberships (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            room_id INTEGER NOT NULL,
            role VARCHAR(20) DEFAULT 'user',
            joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_read_at TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (room_id) REFERENCES chat_rooms (id)
        )
        """)

    # Check if the message_flags table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='message_flags'")
    if not cursor.fetchone():
        print("Creating message_flags table...")
        cursor.execute("""
        CREATE TABLE message_flags (
            id INTEGER PRIMARY KEY,
            message_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            reason VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            resolved BOOLEAN DEFAULT 0,
            resolved_by INTEGER,
            resolved_at TIMESTAMP,
            FOREIGN KEY (message_id) REFERENCES chat_messages (id),
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (resolved_by) REFERENCES users (id)
        )
        """)

    # Check if the prayer_requests table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='prayer_requests'")
    if not cursor.fetchone():
        print("Creating prayer_requests table...")
        cursor.execute("""
        CREATE TABLE prayer_requests (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            title VARCHAR(100) NOT NULL,
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_anonymous BOOLEAN DEFAULT 0,
            is_private BOOLEAN DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """)

    # Check if the prayers table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='prayers'")
    if not cursor.fetchone():
        print("Creating prayers table...")
        cursor.execute("""
        CREATE TABLE prayers (
            id INTEGER PRIMARY KEY,
            prayer_request_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            content TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (prayer_request_id) REFERENCES prayer_requests (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """)

    # Check if the bible_verses table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bible_verses'")
    if not cursor.fetchone():
        print("Creating bible_verses table...")
        cursor.execute("""
        CREATE TABLE bible_verses (
            id INTEGER PRIMARY KEY,
            book VARCHAR(50) NOT NULL,
            chapter INTEGER NOT NULL,
            verse INTEGER NOT NULL,
            text TEXT NOT NULL,
            translation VARCHAR(20) DEFAULT 'KJV',
            reference VARCHAR(50),
            keywords TEXT
        )
        """)

        # Add some common verses
        cursor.execute("""
        INSERT INTO bible_verses (book, chapter, verse, text, reference, keywords)
        VALUES
            ('John', 3, 16, 'For God so loved the world, that he gave his only Son, that whoever believes in him should not perish but have eternal life.', 'John 3:16', 'love,salvation,eternal life'),
            ('Philippians', 4, 7, 'And the peace of God, which surpasses all understanding, will guard your hearts and your minds in Christ Jesus.', 'Philippians 4:7', 'peace,heart,mind'),
            ('Psalm', 23, 1, 'The LORD is my shepherd; I shall not want.', 'Psalm 23:1', 'shepherd,provision'),
            ('Romans', 8, 28, 'And we know that for those who love God all things work together for good, for those who are called according to his purpose.', 'Romans 8:28', 'purpose,good,love'),
            ('Jeremiah', 29, 11, 'For I know the plans I have for you, declares the LORD, plans for welfare and not for evil, to give you a future and a hope.', 'Jeremiah 29:11', 'hope,future,plan')
        """)

    # Check if the filtered_words table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='filtered_words'")
    if not cursor.fetchone():
        print("Creating filtered_words table...")
        cursor.execute("""
        CREATE TABLE filtered_words (
            id INTEGER PRIMARY KEY,
            word VARCHAR(50) NOT NULL UNIQUE,
            replacement VARCHAR(100),
            severity INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """)

        # Add some filtered words with scripture-based replacements
        cursor.execute("""
        INSERT INTO filtered_words (word, replacement, severity)
        VALUES
            ('damn', '"Let no corrupting talk come out of your mouths" - Eph 4:29', 2),
            ('hell', '"Let your speech always be gracious" - Col 4:6', 2),
            ('crap', '"Let no unwholesome talk come out of your mouths" - Eph 4:29', 1),
            ('stupid', '"Whoever says, You fool! will be liable to the hell of fire" - Matt 5:22', 1),
            ('idiot', '"Let all bitterness and wrath and anger and clamor be put away from you" - Eph 4:31', 1)
        """)

    # Check if the friend_requests table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='friend_requests'")
    if not cursor.fetchone():
        print("Creating friend_requests table...")
        cursor.execute("""
        CREATE TABLE friend_requests (
            id INTEGER PRIMARY KEY,
            sender_id INTEGER NOT NULL,
            receiver_id INTEGER NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sender_id) REFERENCES users (id),
            FOREIGN KEY (receiver_id) REFERENCES users (id)
        )
        """)

    # Check if the friendships table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='friendships'")
    if not cursor.fetchone():
        print("Creating friendships table...")
        cursor.execute("""
        CREATE TABLE friendships (
            id INTEGER PRIMARY KEY,
            user_id1 INTEGER NOT NULL,
            user_id2 INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id1) REFERENCES users (id),
            FOREIGN KEY (user_id2) REFERENCES users (id)
        )
        """)

    # Check if the gallery_items table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='gallery_items'")
    if not cursor.fetchone():
        print("Creating gallery_items table...")
        cursor.execute("""
        CREATE TABLE gallery_items (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            title VARCHAR(100) NOT NULL,
            description TEXT,
            item_type VARCHAR(20) NOT NULL,
            content_url VARCHAR(255) NOT NULL,
            thumbnail_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_featured BOOLEAN DEFAULT 0,
            is_approved BOOLEAN DEFAULT 0,
            view_count INTEGER DEFAULT 0,
            like_count INTEGER DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """)

    # Check if the gallery_likes table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='gallery_likes'")
    if not cursor.fetchone():
        print("Creating gallery_likes table...")
        cursor.execute("""
        CREATE TABLE gallery_likes (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            gallery_item_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (gallery_item_id) REFERENCES gallery_items (id)
        )
        """)

    # Check if the gallery_comments table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='gallery_comments'")
    if not cursor.fetchone():
        print("Creating gallery_comments table...")
        cursor.execute("""
        CREATE TABLE gallery_comments (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            gallery_item_id INTEGER NOT NULL,
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (gallery_item_id) REFERENCES gallery_items (id)
        )
        """)

    # Check if the blog_posts table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='blog_posts'")
    if not cursor.fetchone():
        print("Creating blog_posts table...")
        cursor.execute("""
        CREATE TABLE blog_posts (
            id INTEGER PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_id INTEGER,
            is_featured BOOLEAN DEFAULT 0,
            category VARCHAR(50),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """)
    else:
        # Check if the blog_posts table has the is_featured column
        try:
            cursor.execute("SELECT is_featured FROM blog_posts LIMIT 1")
        except sqlite3.OperationalError:
            print("Adding is_featured and category columns to blog_posts table...")
            cursor.execute("ALTER TABLE blog_posts ADD COLUMN is_featured BOOLEAN DEFAULT 0")
            cursor.execute("ALTER TABLE blog_posts ADD COLUMN category VARCHAR(50)")

        # Check if the blog_posts table has the is_published column
        try:
            cursor.execute("SELECT is_published FROM blog_posts LIMIT 1")
        except sqlite3.OperationalError:
            print("Adding is_published column to blog_posts table...")
            cursor.execute("ALTER TABLE blog_posts ADD COLUMN is_published BOOLEAN DEFAULT 1")

        # Check if the blog_posts table has the scheduled_publish_date column
        try:
            cursor.execute("SELECT scheduled_publish_date FROM blog_posts LIMIT 1")
        except sqlite3.OperationalError:
            print("Adding scheduled_publish_date column to blog_posts table...")
            cursor.execute("ALTER TABLE blog_posts ADD COLUMN scheduled_publish_date TIMESTAMP")

    # Check if the users table has the role column
    try:
        cursor.execute("SELECT role FROM users LIMIT 1")
    except sqlite3.OperationalError:
        print("Adding role column to users table...")
        cursor.execute("ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'user'")
        cursor.execute("ALTER TABLE users ADD COLUMN is_banned BOOLEAN DEFAULT 0")
        cursor.execute("ALTER TABLE users ADD COLUMN ban_reason TEXT")

        # Set the first user as admin (using the exact value from the UserRole enum)
        cursor.execute("UPDATE users SET role = 'admin' WHERE id = 1")

    # Check if the users table has the WebAuthn columns
    try:
        cursor.execute("SELECT webauthn_credentials FROM users LIMIT 1")
    except sqlite3.OperationalError:
        print("Adding WebAuthn columns to users table...")
        cursor.execute("ALTER TABLE users ADD COLUMN webauthn_credentials TEXT")
        cursor.execute("ALTER TABLE users ADD COLUMN webauthn_challenge TEXT")

    # Check and fix any users with incorrect role values
    cursor.execute("SELECT id, role FROM users WHERE role NOT IN ('user', 'moderator', 'admin')")
    invalid_roles = cursor.fetchall()
    if invalid_roles:
        print(f"Found {len(invalid_roles)} users with invalid roles. Fixing...")
        for user_id, role in invalid_roles:
            # Convert to lowercase if it's a case issue
            fixed_role = role.lower() if role.upper() in ['USER', 'MODERATOR', 'ADMIN'] else 'user'
            cursor.execute("UPDATE users SET role = ? WHERE id = ?", (fixed_role, user_id))
            print(f"Fixed user {user_id}: changed role from '{role}' to '{fixed_role}'")

    # Commit changes and close connection
    conn.commit()
    conn.close()

    print("Database migration completed successfully!")

async def add_views_column(db: AsyncSession):
    """Add views column to blog_posts if it doesn't exist"""
    try:
        await db.execute(text("ALTER TABLE blog_posts ADD COLUMN views INTEGER DEFAULT 0"))
        await db.commit()
        print("Added views column")
    except Exception as e:
        if "duplicate column" not in str(e).lower():
            raise

async def add_is_featured_column(db: AsyncSession):
    """Add is_featured column to blog_posts if it doesn't exist"""
    try:
        await db.execute(text("ALTER TABLE blog_posts ADD COLUMN is_featured BOOLEAN DEFAULT FALSE"))
        await db.commit()
        print("Added is_featured column")
    except Exception as e:
        if "duplicate column" not in str(e).lower():
            raise

async def add_image_column(db: AsyncSession):
    """Add image column to blog_posts if it doesn't exist"""
    try:
        await db.execute(text("ALTER TABLE blog_posts ADD COLUMN image TEXT"))
        await db.commit()
        print("Added image column")
    except Exception as e:
        if "duplicate column" not in str(e).lower():
            raise

async def add_category_column(db: AsyncSession):
    """Add category column to blog_posts if it doesn't exist"""
    try:
        await db.execute(text("ALTER TABLE blog_posts ADD COLUMN category TEXT"))
        await db.commit()
        print("Added category column")
    except Exception as e:
        if "duplicate column" not in str(e).lower():
            raise

async def fix_sqlite_sequences(db: AsyncSession):
    """Fix SQLite sequences for auto-incrementing IDs"""
    try:
        # Get max IDs
        tables = ['blog_posts', 'blog_comments', 'users', 'prayer_requests']
        for table in tables:
            result = await db.execute(text(f"SELECT MAX(id) FROM {table}"))
            max_id = result.scalar() or 0
            
            # Update sequence
            await db.execute(text(f"UPDATE sqlite_sequence SET seq = {max_id} WHERE name = '{table}'"))
        
        await db.commit()
        print("Fixed SQLite sequences")
    except Exception as e:
        print(f"Error fixing sequences: {e}")
        if "no such table" not in str(e).lower():
            raise

async def update_comment_structure(db: AsyncSession):
    """Update blog comments to support threaded comments"""
    try:
        await db.execute(text("""
            ALTER TABLE blog_comments 
            ADD COLUMN parent_id INTEGER REFERENCES blog_comments(id)
        """))
        await db.commit()
        print("Updated comment structure")
    except Exception as e:
        if "duplicate column" not in str(e).lower():
            raise

async def run_migration():
    await migrate_database()

if __name__ == "__main__":
    asyncio.run(migrate_database())
