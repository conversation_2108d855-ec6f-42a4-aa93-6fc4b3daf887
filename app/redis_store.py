"""
Redis session store for WebAuthn challenges and other session data.
Compatible with Redis 8.
"""

import json
import os
from typing import Any, Dict, Optional, Union
import redis.asyncio as redis

# Simple Redis connection for Redis 8
REDIS_URL = os.environ.get("REDIS_URL", "redis://localhost:6379/0")
redis_client = redis.from_url(REDIS_URL)

# Default expiration time for session data (30 minutes)
DEFAULT_EXPIRATION = 1800  # 30 minutes in seconds

async def set_session_data(session_id: str, key: str, value: Any, expiration: int = DEFAULT_EXPIRATION) -> None:
    """
    Set session data in Redis.

    Args:
        session_id: The session ID
        key: The key to store the data under
        value: The value to store
        expiration: Expiration time in seconds
    """
    # Get existing session data or create a new dictionary
    session_key = f"session:{session_id}"
    session_data = await get_session(session_id) or {}

    # Update the session data
    session_data[key] = value

    # Store the updated session data
    await redis_client.set(session_key, json.dumps(session_data), ex=expiration)

async def get_session_data(session_id: str, key: str) -> Optional[Any]:
    """
    Get session data from Redis.

    Args:
        session_id: The session ID
        key: The key to retrieve

    Returns:
        The stored value or None if not found
    """
    session_data = await get_session(session_id)
    if session_data and key in session_data:
        return session_data[key]
    return None

async def get_session(session_id: str) -> Optional[Dict[str, Any]]:
    """
    Get the entire session data.

    Args:
        session_id: The session ID

    Returns:
        The session data dictionary or None if not found
    """
    session_key = f"session:{session_id}"
    data = await redis_client.get(session_key)
    if data:
        # Handle binary response properly
        if isinstance(data, bytes):
            return json.loads(data.decode('utf-8'))
        return json.loads(data)
    return None

async def delete_session_data(session_id: str, key: str) -> None:
    """
    Delete a specific key from session data.

    Args:
        session_id: The session ID
        key: The key to delete
    """
    session_key = f"session:{session_id}"
    session_data = await get_session(session_id)
    if session_data and key in session_data:
        del session_data[key]
        if session_data:
            await redis_client.set(session_key, json.dumps(session_data), ex=DEFAULT_EXPIRATION)
        else:
            await redis_client.delete(session_key)

async def delete_session(session_id: str) -> None:
    """
    Delete the entire session.

    Args:
        session_id: The session ID to delete
    """
    session_key = f"session:{session_id}"
    await redis_client.delete(session_key)

# WebAuthn specific functions
async def store_webauthn_challenge(user_id: int, challenge: str) -> None:
    """
    Store a WebAuthn challenge for a user.

    Args:
        user_id: The user ID
        challenge: The WebAuthn challenge
    """
    challenge_key = f"webauthn:challenge:{user_id}"
    await redis_client.set(challenge_key, challenge, ex=300)  # 5 minutes expiration

async def get_webauthn_challenge(user_id: int) -> Optional[str]:
    """
    Get a stored WebAuthn challenge for a user.

    Args:
        user_id: The user ID

    Returns:
        The challenge or None if not found
    """
    challenge_key = f"webauthn:challenge:{user_id}"
    challenge = await redis_client.get(challenge_key)
    if challenge:
        # Handle binary response properly
        if isinstance(challenge, bytes):
            return challenge.decode('utf-8')
        return challenge
    return None

async def delete_webauthn_challenge(user_id: int) -> None:
    """
    Delete a stored WebAuthn challenge for a user.

    Args:
        user_id: The user ID
    """
    challenge_key = f"webauthn:challenge:{user_id}"
    await redis_client.delete(challenge_key)

# Redis 8 specific functions utilizing new features
async def set_hash_with_expiry(hash_key: str, field: str, value: Any, expiry: int = DEFAULT_EXPIRATION) -> None:
    """
    Set a hash field with expiration using Redis 8's HSETEX command.

    Args:
        hash_key: The hash key
        field: The field to set
        value: The value to store
        expiry: Expiration time in seconds
    """
    # Convert value to string if needed
    if not isinstance(value, (str, bytes)):
        value = json.dumps(value)

    await redis_client.hsetex(hash_key, field, value, expiry)

async def get_hash_and_delete(hash_key: str, field: str) -> Optional[str]:
    """
    Get a hash field and delete it in one atomic operation using Redis 8's HGETDEL command.

    Args:
        hash_key: The hash key
        field: The field to get and delete

    Returns:
        The value or None if not found
    """
    value = await redis_client.hgetdel(hash_key, field)
    if value and isinstance(value, bytes):
        return value.decode('utf-8')
    return value

async def get_hash_with_expiry(hash_key: str, field: str, expiry: int = DEFAULT_EXPIRATION) -> Optional[str]:
    """
    Get a hash field and reset its expiration in one atomic operation using Redis 8's HGETEX command.

    Args:
        hash_key: The hash key
        field: The field to get
        expiry: New expiration time in seconds

    Returns:
        The value or None if not found
    """
    value = await redis_client.hgetex(hash_key, field, expiry)
    if value and isinstance(value, bytes):
        return value.decode('utf-8')
    return value
