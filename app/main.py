from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Request, Depends, status, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Redirect<PERSON><PERSON>ponse, FileResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.sessions import SessionMiddleware
from starlette.middleware.gzip import GZipMiddleware
from middleware.cache_control import CacheControlMiddleware
from app.middleware import HTMLMinifyMiddleware, DOCTYPECharsetEnforcerMiddleware
import os
import secrets
from datetime import datetime
import logging
from pathlib import Path
import asyncio
import json
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Set, Any
from app.models import User, ChatMessage
from app.auth import decode_access_token, get_current_user, get_user
from app.database import get_db
from sqlalchemy import select

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from .database import init_db, get_db, get_async_db
from .routes import blog, chat, auth, bible, friends, gallery, privacy, upload, bug_reports, faith, notifications
from .routes import events, adverts, daily_verse  # Add new route modules
from .routes import static_pages  # Add static_pages import
# Import contacts separately since we're using its routes directly
from .routes import contacts
# Import Google authentication router
from .routes import google_auth
# Import the ChatManager from websocket.py
from .websocket import ChatManager as WebsocketChatManager
from .models import User, ChatRoom, UserRole, MessageStatus
from .auth import decode_access_token, get_current_user, get_current_user_from_cookie
from .db_migration import run_migration
from .scheduled_tasks import check_scheduled_posts
from .error_handlers import setup_error_handlers
from .sql_logger import setup_sql_logger

# Import shared template configuration
from .template_config import templates

app = FastAPI(
    title="DavarTruth",
    description="A faith-based application with blog and real-time chat features",
    version="0.1.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json"
)

@app.get("/test")
async def test_route():
    return {"status": "ok", "message": "Test route is working"}

@app.get("/test-html")
async def test_html_route():
    """
    Test route that returns a simple HTML page with DOCTYPE
    """
    html_content = """<!DOCTYPE html>
<html>
<head>
    <title>Test HTML</title>
</head>
<body>
    <h1>Test HTML Page</h1>
    <p>This is a test page with DOCTYPE declaration.</p>
</body>
</html>"""

    from fastapi.responses import HTMLResponse
    response = HTMLResponse(content=html_content)
    return response

@app.get("/test-template")
async def test_template_route(request: Request):
    try:
        return app.state.templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "This is a test template.",
                "user": None,
                "is_authenticated": False
            }
        )
    except Exception as e:
        print(f"Error rendering template: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

@app.get("/test-doctype")
async def test_doctype_route(request: Request):
    """
    Test route that returns a simple template with DOCTYPE for debugging
    """
    try:
        # Use the doctype_test.html template we created
        return app.state.templates.TemplateResponse(
            "doctype_test.html",
            {
                "request": request
            }
        )
    except Exception as e:
        print(f"Error in test-doctype route: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

@app.get("/test-title")
async def test_title_route():
    """
    Test route that returns a simple HTML page with a title tag
    """
    html_content = """<!DOCTYPE html>
<html>
<head>
    <title>DavarTruth - Test Title</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <h1>Title Test Page</h1>
    <p>This page has a title tag that should be preserved.</p>
</body>
</html>"""

    from fastapi.responses import HTMLResponse
    response = HTMLResponse(content=html_content)
    return response

# Generate a random secret key for session middleware
SECRET_KEY = secrets.token_hex(32)

@app.on_event("startup")
async def startup():
    try:
        print("Initializing database...")
        await init_db()
        print("Running database migrations...")
        await run_migration()
        print("Database setup completed successfully!")

        # Set up SQL query logging
        setup_sql_logger()
        print("SQL query logging enabled")

        # Start the background task for scheduled posts
        asyncio.create_task(check_scheduled_posts())
        print("Started background task for scheduled posts")
    except Exception as e:
        print(f"Error during startup: {e}")
        import traceback
        traceback.print_exc()

# Include routers
app.include_router(blog.router)
app.include_router(chat.router)
app.include_router(auth.router)
app.include_router(bible.router)
app.include_router(friends.router)
app.include_router(gallery.router)
app.include_router(privacy.router)
app.include_router(upload.router)
app.include_router(bug_reports.router)
app.include_router(faith.router)
app.include_router(events.router)
app.include_router(adverts.router)
app.include_router(daily_verse.router)
app.include_router(static_pages.router)  # Include static_pages router

# Serve static files with optimized handler
from app.optimized_static import OptimizedStaticFiles
from pathlib import Path

# Create static directory if it doesn't exist
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)

# Mount static files middleware with directory creation enabled
app.mount("/static", OptimizedStaticFiles(directory=static_dir, check_dir=True), name="static")

# Simple route for favicon.ico
@app.get('/favicon.ico', include_in_schema=False)
async def favicon():
    """Serve the favicon.ico from the static directory"""
    return FileResponse("static/favicon.ico")

# Simple route for Chrome DevTools JSON
@app.get('/.well-known/appspecific/com.chrome.devtools.json', include_in_schema=False)
async def chrome_devtools_json():
    """Serve a minimal JSON response for Chrome DevTools"""
    return {"version": "1.0"}

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add session middleware
app.add_middleware(SessionMiddleware, secret_key=SECRET_KEY)

app.state.templates = templates

# Set up custom error handlers
setup_error_handlers(app)

# Error handlers
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc):
    return templates.TemplateResponse(
        "errors/404.html",
        {"request": request},
        status_code=404
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    # If using a database, rollback the session
    try:
        db = next(get_db())
        await db.rollback()
    except Exception as e:
        logger.error(f"Error rolling back database: {e}")
    
    return templates.TemplateResponse(
        "errors/500.html",
        {"request": request},
        status_code=500
    )

# Add middleware to set user in request state
class UserMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Get the database session
        async with get_async_db() as db:
            try:
                try:
                    # Get the user from the cookie
                    request.state.user = await get_current_user_from_cookie(request, db)
                except Exception as e:
                    # If there's an error getting the user, set user to None but continue
                    request.state.user = None
                    print(f"Error getting user from cookie: {e}")

                # Continue processing the request
                response = await call_next(request)
                return response
            finally:
                # Note: We don't need to close the db here as it's handled by the context manager
                pass

# Add middlewares in the correct order (last added = first executed)
app.add_middleware(UserMiddleware)

# Add middlewares in the correct order (last added = first executed)

# Add cache control middleware for better performance
app.add_middleware(CacheControlMiddleware)

# Add HTML minification middleware with DOCTYPE preservation
# This should run after DOCTYPE enforcer
app.add_middleware(HTMLMinifyMiddleware)

# Add GZip compression middleware to reduce payload size
# This should be one of the last middleware to run (added early)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Use the ChatManager from websocket.py
from .websocket import ChatManager

# Initialize chat manager
chat_manager = ChatManager()

@app.websocket("/ws/chat/{room_id}")
async def websocket_endpoint(websocket: WebSocket, room_id: str, token: str = None, db: AsyncSession = Depends(get_db)):
    print(f"WebSocket connection attempt for room {room_id} with token: {token}")
    try:
        if not token:
            print("No token provided for WebSocket connection")
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Remove 'Bearer ' prefix if present
        if token.startswith('Bearer '):
            token = token[7:]
            print(f"Removed Bearer prefix, token length: {len(token)}")

        # Verify token and get user
        try:
            # Special case for our dummy token or guest token
            if token == "authenticated_via_meta":
                print("Using authenticated_via_meta token")
                # Get username from the request state or use a default
                username = websocket.cookies.get("username", "Guest")
                print(f"Using username from meta: {username}")
            elif token.startswith("guest_"):
                # Handle guest token (format: guest_username_timestamp)
                print("Using guest token")
                parts = token.split('_')
                if len(parts) >= 2:
                    username = parts[1]
                    print(f"Using username from guest token: {username}")
                else:
                    username = "Guest"
                    print(f"Using default guest username")
            else:
                print(f"Decoding token: {token}")
                payload = decode_access_token(token)
                if payload is None:
                    print("Failed to decode token")
                    await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
                    return

                username = payload.get("sub")
                print(f"Token decoded, username: {username}")
                if not username:
                    print("No username in token payload")
                    await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
                    return

            # Get user from database
            print(f"Getting user from database: {username}")

            # Special case for our dummy token or guest token
            if token == "authenticated_via_meta" or token.startswith("guest_"):
                # Check if we have a user in the meta tag or from guest token
                meta_username = websocket.cookies.get("username") if token == "authenticated_via_meta" else username
                if meta_username:
                    # Try to find the user
                    query = select(User).where(
                        (User.username == meta_username) |
                        (User.email == meta_username) |
                        (User.phone_number == meta_username)
                    )
                    result = await db.execute(query)
                    user = result.scalars().first()

                    if not user:
                        # Create a temporary user object
                        from types import SimpleNamespace
                        user = SimpleNamespace(
                            id=f"guest-{meta_username}",
                            username=meta_username,
                            email=f"{meta_username}@example.com",
                            is_active=True
                        )
                else:
                    # Create a guest user
                    from types import SimpleNamespace
                    user = SimpleNamespace(
                        id="guest-anonymous",
                        username="Guest",
                        email="<EMAIL>",
                        is_active=True
                    )
            else:
                # Use get_user directly instead of get_current_user
                # get_current_user expects a token as first parameter, not a username
                query = select(User).where(
                    (User.username == username) |
                    (User.email == username) |
                    (User.phone_number == username)
                )
                result = await db.execute(query)
                user = result.scalars().first()

                if not user:
                    print(f"User not found in database: {username}")
                    await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
                    return

            print(f"User authenticated: {user.username} (ID: {user.id})")
            user_id = str(user.id)
        except Exception as e:
            print(f"Error authenticating WebSocket connection: {e}")
            import traceback
            traceback.print_exc()
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Connect to chat
        print(f"Connecting user {user.username} to chat room {room_id}")
        await chat_manager.connect(websocket, user_id, room_id, user.username)
        print(f"User {user.username} connected to chat room {room_id}")

        try:
            while True:
                print(f"Waiting for message from user {user.username} in room {room_id}")
                data = await websocket.receive_text()
                print(f"Received data from user {user.username}: {data}")
                message_data = json.loads(data)

                if message_data.get("type") == "message":
                    # Send message to room
                    print(f"Sending message from user {user.username} to room {room_id}: {message_data.get('content', '')}")
                    await chat_manager.send_message(
                        user_id,
                        room_id,
                        message_data.get("content", ""),
                        db
                    )
                elif message_data.get("type") == "typing":
                    # Update typing status
                    print(f"Updating typing status for user {user.username} in room {room_id}: {message_data.get('is_typing', False)}")
                    await chat_manager.set_typing(
                        user_id,
                        room_id,
                        message_data.get("is_typing", False)
                    )
                else:
                    print(f"Unknown message type from user {user.username}: {message_data.get('type')}")
        except WebSocketDisconnect:
            print(f"WebSocket disconnected for user {user.username} in room {room_id}")
            await chat_manager.disconnect(websocket)
        except Exception as e:
            print(f"Error in WebSocket communication for user {user.username}: {e}")
            import traceback
            traceback.print_exc()
            await chat_manager.disconnect(websocket)
    except Exception as e:
        print(f"Unhandled error in WebSocket endpoint: {e}")
        import traceback
        traceback.print_exc()
        try:
            await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
        except Exception as close_error:
            print(f"Error closing WebSocket: {close_error}")

# Root route is handled by the blog router

# Error handlers
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc):
    return templates.TemplateResponse(
        "errors/404.html",
        {"request": request},
        status_code=404
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    # If using a database, rollback the session
    try:
        db = next(get_db())
        await db.rollback()
    except Exception as e:
        logger.error(f"Error rolling back database: {e}")
    
    return templates.TemplateResponse(
        "errors/500.html",
        {"request": request},
        status_code=500
    )

# Configure static files once
static_path = Path(__file__).parent.parent / "static"
app.mount("/static", StaticFiles(directory=str(static_path), html=True), name="static")
