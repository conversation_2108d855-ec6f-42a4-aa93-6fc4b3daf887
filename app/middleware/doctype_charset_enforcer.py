"""
DOCTYPE and Charset Enforcer Middleware

This middleware ensures that all HTML responses have a proper DOCTYPE declaration
and charset specification to prevent browsers from entering quirks mode.
"""

import re
from typing import Callable

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response, StreamingResponse


class DOCTYPECharsetEnforcerMiddleware(BaseHTTPMiddleware):
    """
    Middleware that ensures all HTML responses have a proper DOCTYPE declaration
    and charset specification to prevent browsers from entering quirks mode.
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Process the request and get the response
        response = await call_next(request)

        # Skip processing if the response is not HTML or is compressed
        content_type = response.headers.get("content-type", "")
        content_encoding = response.headers.get("content-encoding", "")

        # Only process uncompressed HTML responses
        if ("text/html" in content_type or not content_type) and not content_encoding:
            # Check if it's a streaming response
            if isinstance(response, StreamingResponse):
                # For streaming responses, we can't easily modify the content
                # Just ensure the content-type header is set correctly
                if "charset" not in content_type:
                    headers = dict(response.headers)
                    headers["content-type"] = "text/html; charset=UTF-8"
                    response.headers = headers
                return response

            # Get the response body
            body = b""
            async for chunk in response.body_iterator:
                body += chunk

            # Decode the body
            try:
                html = body.decode("utf-8")

                # Process all HTML content
                modified = False

                # Remove any leading whitespace
                html = html.lstrip()

                # Check if the HTML has a DOCTYPE declaration
                has_doctype = bool(re.match(r'^\s*<!DOCTYPE[^>]*>', html, re.IGNORECASE))
                if not has_doctype:
                    # Add DOCTYPE if missing
                    html = "<!DOCTYPE html>\n" + html
                    modified = True

                # Check if the HTML has a charset meta tag
                has_charset_meta = bool(re.search(r'<meta[^>]*charset[^>]*>', html, re.IGNORECASE))
                has_content_type_meta = bool(re.search(r'<meta[^>]*http-equiv=["\']Content-Type["\'][^>]*>', html, re.IGNORECASE))

                # If no charset meta tag, add one after the head tag
                if not has_charset_meta and not has_content_type_meta and "<head" in html:
                    head_match = re.search(r'<head[^>]*>', html, re.IGNORECASE)
                    if head_match:
                        head_end = head_match.end()
                        charset_meta = '\n    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">\n    <meta charset="UTF-8">'
                        html = html[:head_end] + charset_meta + html[head_end:]
                        modified = True

                    # If modified, create a new response
                    if modified:
                        # Ensure the content-type header is set correctly
                        headers = dict(response.headers)
                        headers["content-type"] = "text/html; charset=UTF-8"

                        # Create the encoded content
                        encoded_content = html.encode("utf-8")

                        # Remove the Content-Length header to let FastAPI calculate it correctly
                        if "content-length" in headers:
                            del headers["content-length"]

                        # Update the response with the modified HTML
                        response = Response(
                            content=encoded_content,  # Use encoded content directly
                            status_code=response.status_code,
                            headers=headers,
                            media_type="text/html"
                        )
            except UnicodeDecodeError:
                # If we can't decode the body, just return the original response
                pass

        return response
