"""
HTML Minification Middleware for FastAPI
This middleware compresses HTML responses to reduce bandwidth usage and improve load times.
"""

import re
from typing import Callable
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from starlette.responses import Response, StreamingResponse

class HTMLMinifyMiddleware(BaseHTTPMiddleware):
    """
    Middleware that minifies HTML responses to improve performance.
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        # Compile regex patterns for better performance
        self.whitespace_pattern = re.compile(r'>\s+<')
        self.line_break_pattern = re.compile(r'[\n\r\t]+')
        self.comment_pattern = re.compile(r'<!--(?!<!)[^\[>].*?-->')
        self.multiple_spaces_pattern = re.compile(r' {2,}')

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Process the request and get the response
        response = await call_next(request)

        # Skip processing if the response is not HTML or is compressed
        content_type = response.headers.get("content-type", "")
        content_encoding = response.headers.get("content-encoding", "")

        # Only process uncompressed HTML responses and non-error responses
        if content_type and content_type.startswith("text/html") and not content_encoding and response.status_code < 400:
            # Only minify if the response is exactly a starlette.responses.Response (not StreamingResponse or subclasses)
            from starlette.responses import Response as StarletteResponse
            if type(response) is not StarletteResponse:
                return response

            # Get the response body
            body = b""
            async for chunk in response.body_iterator:
                body += chunk

            # Decode the body
            try:
                html = body.decode("utf-8")

                # Skip minification if the page contains <pre> tags or code blocks
                if "<pre" not in html and "<code" not in html:
                    # Check if the HTML has a DOCTYPE declaration
                    has_doctype = bool(re.match(r'^\s*<!DOCTYPE[^>]*>', html, re.IGNORECASE))

                    # For full HTML documents, ensure DOCTYPE is present
                    if not has_doctype:
                        html = "<!DOCTYPE html>\n" + html
                        has_doctype = True

                    # Minify the HTML
                    html = self._minify_html(html)

                    # Ensure the content-type header is set correctly
                    headers = dict(response.headers)
                    headers["content-type"] = "text/html; charset=UTF-8"
                    if "content-length" in headers:
                        del headers["content-length"]

                    encoded_content = html.encode("utf-8")

                    # Preserve the original response class if possible
                    response_cls = type(response)
                    try:
                        response = response_cls(
                            content=encoded_content,
                            status_code=response.status_code,
                            headers=headers,
                            media_type="text/html"
                        )
                    except Exception:
                        # Fallback to generic Response if custom class fails
                        response = Response(
                            content=encoded_content,
                            status_code=response.status_code,
                            headers=headers,
                            media_type="text/html"
                        )
            except UnicodeDecodeError:
                pass

        return response

    def _minify_html(self, html: str) -> str:
        """
        Minify HTML content by removing unnecessary whitespace and comments.

        Args:
            html: The HTML content to minify

        Returns:
            The minified HTML content
        """
        # First, strip any leading whitespace from the entire document
        html = html.lstrip()

        # Check if the HTML starts with DOCTYPE and preserve it
        doctype_match = re.match(r'^<!DOCTYPE[^>]*>', html, re.IGNORECASE)
        doctype = doctype_match.group(0) if doctype_match else "<!DOCTYPE html>"

        # If DOCTYPE exists, remove it temporarily for processing the rest
        if doctype_match:
            html = html[len(doctype_match.group(0)):]

        # Preserve the title tag content
        title_pattern = re.compile(r'<title[^>]*>(.*?)</title>', re.IGNORECASE | re.DOTALL)
        title_match = title_pattern.search(html)
        title_tag = title_match.group(0) if title_match else "<title>DavarTruth</title>"

        # Process iframe content to ensure it has DOCTYPE
        # Find all iframe srcdoc attributes
        iframe_pattern = re.compile(r'<iframe[^>]*srcdoc=["\']([^"\']*)["\'][^>]*>', re.IGNORECASE | re.DOTALL)

        def add_doctype_to_iframe(match):
            iframe_content = match.group(1)
            if not re.match(r'^\s*<!DOCTYPE[^>]*>', iframe_content, re.IGNORECASE):
                # Add DOCTYPE to iframe content if it doesn't have one
                iframe_content = f"<!DOCTYPE html>{iframe_content}"

            # Replace the original srcdoc content with the new one
            return match.group(0).replace(match.group(1), iframe_content)

        # Apply the transformation to all iframe srcdoc attributes
        html = iframe_pattern.sub(add_doctype_to_iframe, html)

        # Remove comments (but preserve conditional comments for IE)
        html = self.comment_pattern.sub('', html)

        # Remove line breaks, tabs, and carriage returns
        html = self.line_break_pattern.sub(' ', html)

        # Remove whitespace between tags
        html = self.whitespace_pattern.sub('><', html)

        # Replace multiple spaces with a single space
        html = self.multiple_spaces_pattern.sub(' ', html)

        # Ensure the title tag is preserved
        if title_match and "<title" not in html:
            # Find the head tag to insert the title
            head_end_match = re.search(r'<head[^>]*>(.*?)</head>', html, re.IGNORECASE | re.DOTALL)
            if head_end_match:
                head_content = head_end_match.group(1)
                new_head_content = head_content + title_tag
                html = html.replace(head_content, new_head_content)
            else:
                # If no head tag, add it after the html tag
                html_tag_match = re.search(r'<html[^>]*>', html, re.IGNORECASE)
                if html_tag_match:
                    html_tag_end = html_tag_match.end()
                    html = html[:html_tag_end] + f"<head>{title_tag}</head>" + html[html_tag_end:]

        # Add DOCTYPE back at the beginning and return
        # Ensure there's absolutely no whitespace before the DOCTYPE
        return doctype + html.strip()
