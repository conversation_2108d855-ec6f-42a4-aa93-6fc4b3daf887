from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship, backref
from datetime import datetime
import enum
from .database import Base

class MessageStatus(str, enum.Enum):
    ACTIVE = "active"
    DELETED = "deleted"
    FLAGGED = "flagged"
    MODERATED = "moderated"

class UserRole(str, enum.Enum):
    USER = "user"
    MODERATOR = "moderator"
    ADMIN = "admin"

class FriendRequestStatus(str, enum.Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    BLOCKED = "blocked"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    phone_number = Column(String(20), nullable=True, unique=True)
    hashed_password = Column(String(100))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    role = Column(String(20), default="user")
    is_banned = Column(Boolean, default=False)
    ban_reason = Column(Text, nullable=True)

    # Profile fields
    full_name = Column(String(100), nullable=True)
    bio = Column(Text, nullable=True)
    profile_picture = Column(String(255), nullable=True)
    location = Column(String(100), nullable=True)
    website = Column(String(255), nullable=True)
    has_completed_profile = Column(Boolean, default=False)

    # WebAuthn fields
    webauthn_credentials = Column(JSON, nullable=True)  # Store WebAuthn credentials
    webauthn_challenge = Column(String(255), nullable=True)  # Store current challenge for WebAuthn

    # Relationships
    messages = relationship("ChatMessage", back_populates="user")
    room_memberships = relationship("UserRoomMembership", back_populates="user")
    prayer_requests = relationship("PrayerRequest", back_populates="user")
    prayers = relationship("Prayer", back_populates="user")
    organized_events = relationship("Event", back_populates="organizer")

class BlogPost(Base):
    __tablename__ = "blog_posts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(100))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    is_featured = Column(Boolean, default=False)
    category = Column(String(50), nullable=True)
    scheduled_publish_date = Column(DateTime, nullable=True)
    is_published = Column(Boolean, default=True)
    is_draft = Column(Boolean, default=False)  # New field for drafts
    is_scheduled = Column(Boolean, default=False)  # New field specifically for scheduled posts

    # Relationships
    author = relationship("User", foreign_keys=[user_id])
    comments = relationship("BlogComment", back_populates="post")

class ChatRoom(Base):
    __tablename__ = "chat_rooms"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True)
    description = Column(Text, nullable=True)
    room_type = Column(String(50), default="general")  # general, prayer, study, etc.
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    is_private = Column(Boolean, default=False)

    # Relationships
    messages = relationship("ChatMessage", back_populates="room")
    creator = relationship("User")

class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text)
    username = Column(String(50))  # Simple identifier for chat
    created_at = Column(DateTime, default=datetime.utcnow)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Link to user
    room_id = Column(Integer, ForeignKey("chat_rooms.id"), nullable=True)  # Link to chat room
    # Use String instead of Enum for better SQLite compatibility
    status = Column(String(20), default="active")
    parent_id = Column(Integer, ForeignKey("chat_messages.id"), nullable=True)  # For threaded replies

    # Relationships
    user = relationship("User", back_populates="messages")
    room = relationship("ChatRoom", back_populates="messages")
    replies = relationship("ChatMessage", backref="parent", remote_side=[id])
    flags = relationship("MessageFlag", back_populates="message")

class MessageFlag(Base):
    __tablename__ = "message_flags"

    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("chat_messages.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    reason = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)
    resolved = Column(Boolean, default=False)
    resolved_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    resolved_at = Column(DateTime, nullable=True)

    # Relationships
    message = relationship("ChatMessage", back_populates="flags")
    reporter = relationship("User", foreign_keys=[user_id])
    resolver = relationship("User", foreign_keys=[resolved_by])

class BibleVerse(Base):
    __tablename__ = "bible_verses"

    id = Column(Integer, primary_key=True, index=True)
    book = Column(String(50))
    chapter = Column(Integer)
    verse = Column(Integer)
    text = Column(Text)
    translation = Column(String(20), default="KJV")

    # For quick lookup
    reference = Column(String(50), index=True)  # e.g., "John 3:16"
    keywords = Column(Text, nullable=True)  # Comma-separated keywords

class UserRoomMembership(Base):
    __tablename__ = "user_room_memberships"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    room_id = Column(Integer, ForeignKey("chat_rooms.id"))
    # Use String instead of Enum for better SQLite compatibility
    role = Column(String(20), default="user")
    joined_at = Column(DateTime, default=datetime.utcnow)
    last_read_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User")
    room = relationship("ChatRoom")

class PrayerRequest(Base):
    __tablename__ = "prayer_requests"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String(100))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_anonymous = Column(Boolean, default=False)
    is_private = Column(Boolean, default=False)  # If True, only visible to admins/moderators

    # Relationships
    user = relationship("User")
    prayers = relationship("Prayer", back_populates="prayer_request")

class Prayer(Base):
    __tablename__ = "prayers"

    id = Column(Integer, primary_key=True, index=True)
    prayer_request_id = Column(Integer, ForeignKey("prayer_requests.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    content = Column(Text, nullable=True)  # Optional prayer content
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    prayer_request = relationship("PrayerRequest", back_populates="prayers")
    user = relationship("User")


class BibleStudyPost(Base):
    __tablename__ = "bible_study_posts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String(100))
    content = Column(Text)
    verse_reference = Column(String(100), nullable=True)  # Optional Bible verse reference
    created_at = Column(DateTime, default=datetime.utcnow)
    is_anonymous = Column(Boolean, default=False)
    is_private = Column(Boolean, default=False)  # If True, only visible to admins/moderators

    # Relationships
    user = relationship("User")
    comments = relationship("BibleStudyComment", back_populates="post")


class BibleStudyComment(Base):
    __tablename__ = "bible_study_comments"

    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("bible_study_posts.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    post = relationship("BibleStudyPost", back_populates="comments")
    user = relationship("User")


class GeneralPost(Base):
    __tablename__ = "general_posts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String(100))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_anonymous = Column(Boolean, default=False)
    is_private = Column(Boolean, default=False)  # If True, only visible to admins/moderators

    # Relationships
    user = relationship("User")
    comments = relationship("GeneralComment", back_populates="post")


class GeneralComment(Base):
    __tablename__ = "general_comments"

    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("general_posts.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    post = relationship("GeneralPost", back_populates="comments")
    user = relationship("User")

class FilteredWord(Base):
    __tablename__ = "filtered_words"

    id = Column(Integer, primary_key=True, index=True)
    word = Column(String(50), unique=True)
    replacement = Column(String(100), nullable=True)  # Optional replacement text
    severity = Column(Integer, default=1)  # 1=mild, 2=moderate, 3=severe
    created_at = Column(DateTime, default=datetime.utcnow)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    # Relationship
    creator = relationship("User")

class FriendRequest(Base):
    __tablename__ = "friend_requests"

    id = Column(Integer, primary_key=True, index=True)
    sender_id = Column(Integer, ForeignKey("users.id"))
    receiver_id = Column(Integer, ForeignKey("users.id"))
    # Use String instead of Enum for better SQLite compatibility
    status = Column(String(20), default="pending")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    sender = relationship("User", foreign_keys=[sender_id])
    receiver = relationship("User", foreign_keys=[receiver_id])

class Friendship(Base):
    __tablename__ = "friendships"

    id = Column(Integer, primary_key=True, index=True)
    user_id1 = Column(Integer, ForeignKey("users.id"))
    user_id2 = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user1 = relationship("User", foreign_keys=[user_id1])
    user2 = relationship("User", foreign_keys=[user_id2])

class GalleryItemType(enum.Enum):
    IMAGE = "image"
    QUOTE = "quote"
    VIDEO = "video"
    AUDIO = "audio"

class GalleryItem(Base):
    __tablename__ = "gallery_items"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String(100))
    description = Column(Text, nullable=True)
    # Use String instead of Enum for better SQLite compatibility
    item_type = Column(String(20))
    content_url = Column(String(255))  # URL to the image, video, etc.
    thumbnail_url = Column(String(255), nullable=True)  # For videos, etc.
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_featured = Column(Boolean, default=False)
    is_approved = Column(Boolean, default=False)  # For moderation
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)

    # Relationships
    user = relationship("User")
    likes = relationship("GalleryLike", back_populates="gallery_item")
    comments = relationship("GalleryComment", back_populates="gallery_item")

class GalleryLike(Base):
    __tablename__ = "gallery_likes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    gallery_item_id = Column(Integer, ForeignKey("gallery_items.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User")
    gallery_item = relationship("GalleryItem", back_populates="likes")

class GalleryComment(Base):
    __tablename__ = "gallery_comments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    gallery_item_id = Column(Integer, ForeignKey("gallery_items.id"))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User")
    gallery_item = relationship("GalleryItem", back_populates="comments")

class BlogComment(Base):
    __tablename__ = "blog_comments"

    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("blog_posts.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    content = Column(Text)
    parent_id = Column(Integer, ForeignKey("blog_comments.id"), nullable=True)  # For replies
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    post = relationship("BlogPost", back_populates="comments")
    user = relationship("User")
    replies = relationship("BlogComment", backref=backref("parent", remote_side=[id]), cascade="all, delete-orphan")

class ContentReport(Base):
    __tablename__ = "content_reports"

    id = Column(Integer, primary_key=True, index=True)
    content_type = Column(String(50))  # blog_post, comment, gallery_item, etc.
    content_id = Column(Integer)  # ID of the reported content
    user_id = Column(Integer, ForeignKey("users.id"))
    reason = Column(String(100))
    details = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String(20), default="new")  # new, in-progress, resolved, closed
    resolved_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    resolved_at = Column(DateTime, nullable=True)

    # Relationships
    reporter = relationship("User", foreign_keys=[user_id])
    resolver = relationship("User", foreign_keys=[resolved_by])

class BugReport(Base):
    __tablename__ = "bug_reports"

    id = Column(Integer, primary_key=True, index=True)
    bug_type = Column(String(50), nullable=False)
    description = Column(Text, nullable=False)
    steps_to_reproduce = Column(Text)
    email = Column(String(255))
    browser_info = Column(JSON)
    status = Column(String(20), default="new")  # new, in-progress, resolved, closed
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Contact(Base):
    __tablename__ = "contacts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    contact_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Can be null for external contacts
    nickname = Column(String(50), nullable=True)  # Optional nickname for the contact
    phone = Column(String(20), nullable=True)  # Optional phone number for the contact
    external_username = Column(String(50), nullable=True)  # For contacts not registered in the system
    created_at = Column(DateTime, default=datetime.utcnow)
    is_favorite = Column(Boolean, default=False)  # Mark as favorite contact
    is_blocked = Column(Boolean, default=False)  # Block contact
    notes = Column(Text, nullable=True)  # Private notes about the contact

    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="contacts")
    contact = relationship("User", foreign_keys=[contact_id])

class NotificationType(str, enum.Enum):
    NEW_MESSAGE = "new_message"
    DIRECT_MESSAGE = "direct_message"
    FRIEND_REQUEST = "friend_request"
    PRAYER_REQUEST = "prayer_request"
    COMMENT_REPLY = "comment_reply"
    SYSTEM = "system"

class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    notification_type = Column(String(50))  # Using the NotificationType enum values
    content = Column(Text)  # Notification message
    link = Column(String(255), nullable=True)  # Link to the relevant content
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    data = Column(JSON, nullable=True)  # Additional data in JSON format

    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="notifications")
    sender = relationship("User", foreign_keys=[sender_id])

class DirectMessage(Base):
    __tablename__ = "direct_messages"

    id = Column(Integer, primary_key=True, index=True)
    sender_id = Column(Integer, ForeignKey("users.id"))
    receiver_id = Column(Integer, ForeignKey("users.id"))
    content = Column(Text)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String(20), default="active")  # active, deleted, etc.
    is_voice_message = Column(Boolean, default=False)  # Flag for voice messages
    voice_duration = Column(Integer, nullable=True)  # Duration in seconds for voice messages

    # Relationships
    sender = relationship("User", foreign_keys=[sender_id])
    receiver = relationship("User", foreign_keys=[receiver_id])

class Event(Base):
    __tablename__ = "events"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200))  # The event title
    description = Column(Text)  # Event description
    event_date = Column(DateTime)  # When the event will happen
    created_at = Column(DateTime, default=datetime.utcnow)  # When the event was created
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    link = Column(String(500), nullable=True)  # Optional link for more information
    location = Column(String(200), nullable=True)  # Optional location information
    organizer_id = Column(Integer, ForeignKey("users.id"))

    # Relationships
    organizer = relationship("User", back_populates="organized_events")

class Advert(Base):
    __tablename__ = "adverts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200))
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)  
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    link = Column(String(500), nullable=True)  # Optional link for more information
    link_text = Column(String(100), default="View Details")
    expiry_date = Column(DateTime)  # When the advert should expire
    created_by = Column(Integer, ForeignKey("users.id"))

    # Relationships
    creator = relationship("User", foreign_keys=[created_by])
