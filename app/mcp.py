"""
Memory Control Protocol (MCP) for vessels memory.

This module provides the core functionality for the Memory Control Protocol,
which manages memory storage, retrieval, and synchronization.
Compatible with Redis 8.
"""

import json
import os
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

import redis.asyncio as redis
# Import only what we need

# Redis connection with improved configuration for Redis 8
REDIS_URL = os.environ.get("REDIS_URL", "redis://localhost:6379/0")
# Initialize Redis client with health check
redis_client = redis.from_url(
    REDIS_URL,
    health_check_interval=30,
    decode_responses=False  # Keep binary responses for proper handling
)

# Constants
MEMORY_PREFIX = "vessels:memory:"
MEMORY_INDEX = f"{MEMORY_PREFIX}index"
DEFAULT_TTL = 60 * 60 * 24 * 30  # 30 days in seconds

class MemoryType:
    """Enum for memory types."""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    LINK = "link"
    FILE = "file"
    MIXED = "mixed"

class MemoryStatus:
    """Enum for memory status."""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"

async def create_memory(
    content: Union[str, Dict[str, Any]],
    user_id: int,
    memory_type: str = MemoryType.TEXT,
    tags: List[str] = None,
    metadata: Dict[str, Any] = None,
    ttl: int = DEFAULT_TTL
) -> Dict[str, Any]:
    """
    Create a new memory.

    Args:
        content: The memory content (text or structured data)
        user_id: The ID of the user creating the memory
        memory_type: The type of memory (text, image, etc.)
        tags: Optional list of tags for categorization
        metadata: Optional metadata for the memory
        ttl: Time-to-live in seconds (default: 30 days)

    Returns:
        The created memory object
    """
    # Generate a unique ID for the memory
    memory_id = str(uuid.uuid4())

    # Create the memory object
    memory = {
        "id": memory_id,
        "content": content,
        "user_id": user_id,
        "type": memory_type,
        "tags": tags or [],
        "metadata": metadata or {},
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
        "status": MemoryStatus.ACTIVE
    }

    # Store the memory in Redis
    memory_key = f"{MEMORY_PREFIX}{memory_id}"
    await redis_client.set(memory_key, json.dumps(memory), ex=ttl)

    # Add to the memory index
    await redis_client.sadd(MEMORY_INDEX, memory_id)

    # If tags are provided, add to tag indices
    if tags:
        for tag in tags:
            tag_key = f"{MEMORY_PREFIX}tag:{tag}"
            await redis_client.sadd(tag_key, memory_id)

    # Add to user's memories
    user_memories_key = f"{MEMORY_PREFIX}user:{user_id}"
    await redis_client.sadd(user_memories_key, memory_id)

    return memory

async def get_memory(memory_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve a memory by ID.

    Args:
        memory_id: The ID of the memory to retrieve

    Returns:
        The memory object or None if not found
    """
    memory_key = f"{MEMORY_PREFIX}{memory_id}"
    memory_data = await redis_client.get(memory_key)

    if not memory_data:
        return None

    # Handle binary response properly
    if isinstance(memory_data, bytes):
        return json.loads(memory_data.decode('utf-8'))
    return json.loads(memory_data)

async def update_memory(
    memory_id: str,
    content: Union[str, Dict[str, Any]] = None,
    memory_type: str = None,
    tags: List[str] = None,
    metadata: Dict[str, Any] = None,
    status: str = None,
    ttl: int = DEFAULT_TTL
) -> Optional[Dict[str, Any]]:
    """
    Update an existing memory.

    Args:
        memory_id: The ID of the memory to update
        content: New content (optional)
        memory_type: New memory type (optional)
        tags: New tags (optional)
        metadata: New metadata (optional)
        status: New status (optional)
        ttl: New time-to-live in seconds (optional)

    Returns:
        The updated memory object or None if not found
    """
    # Get the existing memory
    memory = await get_memory(memory_id)
    if not memory:
        return None

    # Update fields if provided
    if content is not None:
        memory["content"] = content

    if memory_type is not None:
        memory["type"] = memory_type

    if tags is not None:
        # Remove from old tag indices
        for tag in memory["tags"]:
            tag_key = f"{MEMORY_PREFIX}tag:{tag}"
            await redis_client.srem(tag_key, memory_id)

        # Add to new tag indices
        memory["tags"] = tags
        for tag in tags:
            tag_key = f"{MEMORY_PREFIX}tag:{tag}"
            await redis_client.sadd(tag_key, memory_id)

    if metadata is not None:
        memory["metadata"] = metadata

    if status is not None:
        memory["status"] = status

    # Update the timestamp
    memory["updated_at"] = datetime.now(timezone.utc).isoformat()

    # Store the updated memory
    memory_key = f"{MEMORY_PREFIX}{memory_id}"
    await redis_client.set(memory_key, json.dumps(memory), ex=ttl)

    return memory

async def delete_memory(memory_id: str, hard_delete: bool = False) -> bool:
    """
    Delete a memory.

    Args:
        memory_id: The ID of the memory to delete
        hard_delete: If True, permanently delete; if False, mark as deleted

    Returns:
        True if successful, False if memory not found
    """
    memory = await get_memory(memory_id)
    if not memory:
        return False

    if hard_delete:
        # Remove from all indices
        await redis_client.srem(MEMORY_INDEX, memory_id)

        # Remove from tag indices
        for tag in memory["tags"]:
            tag_key = f"{MEMORY_PREFIX}tag:{tag}"
            await redis_client.srem(tag_key, memory_id)

        # Remove from user's memories
        user_memories_key = f"{MEMORY_PREFIX}user:{memory['user_id']}"
        await redis_client.srem(user_memories_key, memory_id)

        # Delete the memory
        memory_key = f"{MEMORY_PREFIX}{memory_id}"
        await redis_client.delete(memory_key)
    else:
        # Soft delete - just update the status
        await update_memory(memory_id, status=MemoryStatus.DELETED)

    return True

async def list_memories(
    user_id: Optional[int] = None,
    tags: Optional[List[str]] = None,
    memory_type: Optional[str] = None,
    status: str = MemoryStatus.ACTIVE,
    limit: int = 100,
    offset: int = 0
) -> List[Dict[str, Any]]:
    """
    List memories with optional filtering.

    Args:
        user_id: Filter by user ID (optional)
        tags: Filter by tags (optional)
        memory_type: Filter by memory type (optional)
        status: Filter by status (default: active)
        limit: Maximum number of results
        offset: Pagination offset

    Returns:
        List of memory objects
    """
    # Start with the full index or user-specific index
    if user_id is not None:
        base_key = f"{MEMORY_PREFIX}user:{user_id}"
    else:
        base_key = MEMORY_INDEX

    # Get memory IDs from the base index
    memory_ids = await redis_client.smembers(base_key)
    # Handle binary responses properly
    memory_ids = [mid.decode('utf-8') if isinstance(mid, bytes) else mid for mid in memory_ids]

    # Filter by tags if specified
    if tags:
        tag_memory_ids = set(memory_ids)
        for tag in tags:
            tag_key = f"{MEMORY_PREFIX}tag:{tag}"
            tag_ids = await redis_client.smembers(tag_key)
            # Handle binary responses properly
            tag_ids = {tid.decode('utf-8') if isinstance(tid, bytes) else tid for tid in tag_ids}
            tag_memory_ids &= tag_ids
        memory_ids = list(tag_memory_ids)

    # Retrieve all memories
    memories = []
    for memory_id in memory_ids:
        memory = await get_memory(memory_id)
        if memory:
            # Filter by type and status
            if (memory_type is None or memory["type"] == memory_type) and \
               (status is None or memory["status"] == status):
                memories.append(memory)

    # Sort by creation time (newest first)
    memories.sort(key=lambda m: m["created_at"], reverse=True)

    # Apply pagination
    return memories[offset:offset+limit]

async def search_memories(
    query: str,
    user_id: Optional[int] = None,
    tags: Optional[List[str]] = None,
    memory_type: Optional[str] = None,
    status: str = MemoryStatus.ACTIVE,
    limit: int = 100,
    offset: int = 0
) -> List[Dict[str, Any]]:
    """
    Search memories by content.

    Args:
        query: Search query string
        user_id: Filter by user ID (optional)
        tags: Filter by tags (optional)
        memory_type: Filter by memory type (optional)
        status: Filter by status (default: active)
        limit: Maximum number of results
        offset: Pagination offset

    Returns:
        List of matching memory objects
    """
    # Get filtered memories
    memories = await list_memories(user_id, tags, memory_type, status)

    # Filter by search query
    query = query.lower()
    results = []

    for memory in memories:
        # Search in content
        content = memory["content"]
        if isinstance(content, str) and query in content.lower():
            results.append(memory)
        elif isinstance(content, dict):
            # Search in dictionary values
            for value in content.values():
                if isinstance(value, str) and query in value.lower():
                    results.append(memory)
                    break

        # Search in tags
        if memory not in results:
            for tag in memory["tags"]:
                if query in tag.lower():
                    results.append(memory)
                    break

    # Apply pagination
    return results[offset:offset+limit]

async def add_tag_to_memory(memory_id: str, tag: str) -> Optional[Dict[str, Any]]:
    """
    Add a tag to a memory.

    Args:
        memory_id: The ID of the memory
        tag: The tag to add

    Returns:
        The updated memory object or None if not found
    """
    memory = await get_memory(memory_id)
    if not memory:
        return None

    if tag not in memory["tags"]:
        memory["tags"].append(tag)

        # Add to tag index
        tag_key = f"{MEMORY_PREFIX}tag:{tag}"
        await redis_client.sadd(tag_key, memory_id)

        # Update the memory
        await update_memory(memory_id, tags=memory["tags"])

    return memory

async def remove_tag_from_memory(memory_id: str, tag: str) -> Optional[Dict[str, Any]]:
    """
    Remove a tag from a memory.

    Args:
        memory_id: The ID of the memory
        tag: The tag to remove

    Returns:
        The updated memory object or None if not found
    """
    memory = await get_memory(memory_id)
    if not memory:
        return None

    if tag in memory["tags"]:
        memory["tags"].remove(tag)

        # Remove from tag index
        tag_key = f"{MEMORY_PREFIX}tag:{tag}"
        await redis_client.srem(tag_key, memory_id)

        # Update the memory
        await update_memory(memory_id, tags=memory["tags"])

    return memory

async def get_memory_by_content(
    content: str,
    user_id: Optional[int] = None,
    exact_match: bool = False
) -> Optional[Dict[str, Any]]:
    """
    Find a memory by its content.

    Args:
        content: The content to search for
        user_id: Filter by user ID (optional)
        exact_match: If True, require exact content match; if False, search within content

    Returns:
        The matching memory object or None if not found
    """
    # Get memories for the user or all memories
    memories = await list_memories(user_id=user_id)

    for memory in memories:
        if isinstance(memory["content"], str):
            if exact_match and memory["content"] == content:
                return memory
            elif not exact_match and content in memory["content"]:
                return memory

    return None
