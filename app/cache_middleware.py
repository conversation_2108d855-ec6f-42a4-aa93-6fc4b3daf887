"""
Cache Middleware for DavarTruth

This middleware adds appropriate caching headers to responses based on content type
and path to improve performance and reduce server load.
"""

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import time
from typing import Dict, List, Tuple
import re
from functools import wraps
import hashlib
import json
from redis import Redis
from app.redis_store import redis_client

CACHE_TTL = 3600  # 1 hour default TTL

class CacheControlMiddleware(BaseHTTPMiddleware):
    """
    Middleware that adds Cache-Control headers to responses.
    
    This improves performance by allowing browsers to cache static resources
    and reduces server load by avoiding unnecessary requests.
    """
    
    def __init__(self, app, **options):
        super().__init__(app)
        self.cache_rules = [
            # Format: (path_pattern, max_age_seconds, cache_control_directives)
            
            # Static assets with long cache times (1 year)
            (r"^/static/icons/.*\.(png|jpg|jpeg|gif|webp|svg)$", 31536000, "public, max-age=31536000, immutable"),
            (r"^/static/.*\.(woff2|woff|ttf|eot)$", 31536000, "public, max-age=31536000, immutable"),
            
            # Static CSS and JS with medium cache times (1 week)
            (r"^/static/css/.*\.css$", 604800, "public, max-age=604800"),
            (r"^/static/js/.*\.js$", 604800, "public, max-age=604800"),
            
            # Images with medium cache times (1 week)
            (r"^/static/.*\.(png|jpg|jpeg|gif|webp|svg)$", 604800, "public, max-age=604800"),
            
            # Service worker with short cache time (1 hour)
            (r"^/static/sw\.js$", 3600, "public, max-age=3600"),
            
            # Manifest with medium cache time (1 day)
            (r"^/static/manifest\.json$", 86400, "public, max-age=86400"),
            
            # HTML pages should not be cached by default
            (r"\.html$", 0, "no-store, must-revalidate"),
            
            # API responses should not be cached
            (r"^/api/", 0, "no-store, must-revalidate"),
        ]
        
        # Content types that should have specific cache headers
        self.content_type_rules = {
            "text/html": "no-store, must-revalidate",
            "application/json": "no-store, must-revalidate",
            "image/": "public, max-age=604800",  # 1 week for images
            "text/css": "public, max-age=604800",  # 1 week for CSS
            "application/javascript": "public, max-age=604800",  # 1 week for JS
            "font/": "public, max-age=31536000, immutable",  # 1 year for fonts
            "application/font": "public, max-age=31536000, immutable",  # 1 year for fonts
        }
    
    async def dispatch(self, request: Request, call_next):
        # Process the request and get the response
        response = await call_next(request)
        
        # Skip if response already has Cache-Control header
        if "Cache-Control" in response.headers:
            return response
            
        # Get the path from the request
        path = request.url.path
        
        # Check if the path matches any of our cache rules
        for pattern, max_age, cache_control in self.cache_rules:
            if re.search(pattern, path):
                response.headers["Cache-Control"] = cache_control
                
                # Add Expires header for HTTP/1.0 compatibility
                if max_age > 0:
                    expires = time.strftime(
                        "%a, %d %b %Y %H:%M:%S GMT", 
                        time.gmtime(time.time() + max_age)
                    )
                    response.headers["Expires"] = expires
                
                # Add Vary header for proper caching
                response.headers["Vary"] = "Accept-Encoding"
                
                return response
        
        # If no path match, check content type
        content_type = response.headers.get("Content-Type", "")
        for ct_prefix, cache_control in self.content_type_rules.items():
            if content_type.startswith(ct_prefix):
                response.headers["Cache-Control"] = cache_control
                response.headers["Vary"] = "Accept-Encoding"
                return response
        
        # Default: no caching for unmatched responses
        response.headers["Cache-Control"] = "no-store, must-revalidate"
        return response

def cache_response(ttl=CACHE_TTL):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key from function name and arguments
            key_parts = [func.__name__]
            key_parts.extend([str(arg) for arg in args])
            key_parts.extend([f"{k}:{v}" for k, v in kwargs.items()])
            cache_key = hashlib.md5(":".join(key_parts).encode()).hexdigest()

            # Try to get from cache
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)

            # Get fresh data
            result = await func(*args, **kwargs)
            
            # Cache the result
            redis_client.setex(
                cache_key,
                ttl,
                json.dumps(result)
            )
            
            return result
        return wrapper
    return decorator

async def rate_limit_middleware(request: Request, call_next):
    # Get client IP
    client_ip = request.client.host
    
    # Rate limit key includes the IP and endpoint
    rate_key = f"rate:{client_ip}:{request.url.path}"
    
    # Check rate limit (100 requests per minute)
    requests = redis_client.get(rate_key)
    if requests and int(requests) > 100:
        return Response(
            content=json.dumps({"error": "Rate limit exceeded"}),
            status_code=429
        )
    
    # Increment request count
    redis_client.incr(rate_key)
    redis_client.expire(rate_key, 60)  # Expire after 1 minute
    
    response = await call_next(request)
    return response
