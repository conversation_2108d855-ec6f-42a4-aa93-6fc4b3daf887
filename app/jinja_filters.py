"""
Custom Jinja2 filters for the application
"""
import json
import re
from urllib.parse import urljoin

def escapejs(value):
    """
    Escapes characters for use in JavaScript strings.
    This is similar to Django's escapejs filter.
    """
    if not isinstance(value, str):
        return value
    
    # Replace backslashes with double backslashes
    value = value.replace('\\', '\\\\')
    
    # Replace single quotes with escaped single quotes
    value = value.replace("'", "\\'")
    
    # Replace double quotes with escaped double quotes
    value = value.replace('"', '\\"')
    
    # Replace newlines with \n
    value = value.replace('\n', '\\n')
    
    # Replace carriage returns with \r
    value = value.replace('\r', '\\r')
    
    # Replace tabs with \t
    value = value.replace('\t', '\\t')
    
    # Replace form feeds with \f
    value = value.replace('\f', '\\f')
    
    # Replace backspace with \b
    value = value.replace('\b', '\\b')
    
    # Replace other control characters
    def replace_control(match):
        char = match.group(0)
        code = ord(char)
        return f'\\u{code:04x}'
    
    value = re.sub(r'[\x00-\x1F\x7F]', replace_control, value)
    return value

def static_url(path):
    """
    Generate URL for static files.
    This is similar to url_for('static', filename=path) but simpler.
    """
    return urljoin('/static/', path.lstrip('/'))
