#!/bin/bash

echo "VS Code Reset and Optimization Script"
echo "===================================="

# Create backup of VS Code settings
echo "Creating backup of VS Code settings..."
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SETTINGS_DIR="$HOME/.config/Code/User"
BACKUP_DIR="$HOME/vscode_backup_$TIMESTAMP"

mkdir -p "$BACKUP_DIR"
if [ -d "$SETTINGS_DIR" ]; then
  cp -r "$SETTINGS_DIR" "$BACKUP_DIR/"
  echo "Settings backed up to $BACKUP_DIR"
else
  echo "Settings directory not found at $SETTINGS_DIR"
fi

# Clear VS Code caches
echo "Clearing VS Code caches..."
rm -rf "$HOME/.config/Code/Cache"
rm -rf "$HOME/.config/Code/CachedData"
rm -rf "$HOME/.config/Code/CachedExtensions"
rm -rf "$HOME/.config/Code/Code Cache"
rm -rf "$HOME/.config/Code/Service Worker"

# Create or update VS Code settings to optimize performance
echo "Updating VS Code settings for better performance..."
SETTINGS_FILE="$SETTINGS_DIR/settings.json"

# Ensure settings directory exists
mkdir -p "$SETTINGS_DIR"

# Backup existing settings file if it exists
if [ -f "$SETTINGS_FILE" ]; then
  cp "$SETTINGS_FILE" "$BACKUP_DIR/settings.json.bak"
fi

# Create or update settings file with optimized settings
cat > "$SETTINGS_FILE" <<EOL
{
    // Editor performance settings
    "editor.minimap.enabled": false,
    "editor.renderWhitespace": "none",
    "editor.renderControlCharacters": false,
    "editor.renderLineHighlight": "none",
    "workbench.enableExperiments": false,
    "workbench.settings.enableNaturalLanguageSearch": false,
    "update.mode": "manual",
    "telemetry.telemetryLevel": "off",
    "extensions.autoUpdate": false,
    "window.autoDetectColorScheme": false,
    
    // WebGL settings
    "webgl.disabled": true,
    
    // Extension host settings
    "extensions.autoCheckUpdates": false,
    "extensions.ignoreRecommendations": true,
    
    // Keep your existing custom settings below
}
EOL

echo "Settings updated with performance optimizations"

# Create VS Code launch script with hardware acceleration disabled
echo "Creating optimized VS Code launch script..."
LAUNCH_SCRIPT="$HOME/launch_vscode_optimized.sh"

cat > "$LAUNCH_SCRIPT" <<EOL
#!/bin/bash
code --disable-gpu --disable-extensions --disable-software-rasterizer --disable-gpu-compositing --disable-gpu-rasterization --disable-gpu-sandbox --disable-smooth-scrolling --disable-2d-canvas-clip-aa --disable-dev-shm-usage --disable-webgl --enable-unsafe-webgpu --js-flags="--max-old-space-size=4096" "\$@"
EOL

chmod +x "$LAUNCH_SCRIPT"
echo "Created optimized launch script at $LAUNCH_SCRIPT"

echo ""
echo "Reset complete. Please restart VS Code using the optimized launch script:"
echo "$ $LAUNCH_SCRIPT"
echo ""
echo "If you experience issues with this configuration, your original settings have been backed up to $BACKUP_DIR"
