#!/bin/bash

echo "Augment Extension Fix Script"
echo "==========================="

# Define VS Code extension directory
VSCODE_EXTENSIONS_DIR="$HOME/.vscode/extensions"
AUGMENT_EXTENSION_DIR=$(find "$VSCODE_EXTENSIONS_DIR" -maxdepth 1 -type d -name "augment.vscode-augment-*" | sort -r | head -n 1)

# Check if VS Code is running
if pgrep -x "code" > /dev/null; then
  echo "VS Code is currently running. Please close VS Code before continuing."
  echo "Run 'killall code' to force close VS Code."
  exit 1
fi

# Check if Augment extension is installed
if [ -z "$AUGMENT_EXTENSION_DIR" ]; then
  echo "Augment extension not found in $VSCODE_EXTENSIONS_DIR"
  echo "Please make sure the extension is installed."
  exit 1
fi

echo "Found Augment extension at: $AUGMENT_EXTENSION_DIR"

# Backup Augment extension directory
echo "Creating backup of Augment extension directory..."
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="$HOME/augment_ext_backup_$TIMESTAMP"

mkdir -p "$BACKUP_DIR"
cp -r "$AUGMENT_EXTENSION_DIR" "$BACKUP_DIR/"
echo "Augment extension backed up to $BACKUP_DIR"

# Clean Augment extension cache
echo "Cleaning Augment extension cache..."
rm -rf "$AUGMENT_EXTENSION_DIR/dist/webview/service-worker.js"*
rm -rf "$AUGMENT_EXTENSION_DIR/dist/webview/worker.js"*
echo "Augment extension cache cleaned"

# Create VS Code settings to optimize Augment extension
echo "Creating optimized settings for Augment extension..."
SETTINGS_DIR="$HOME/.config/Code/User"
SETTINGS_FILE="$SETTINGS_DIR/settings.json"

# Ensure settings directory exists
mkdir -p "$SETTINGS_DIR"

# Backup existing settings file if it exists
if [ -f "$SETTINGS_FILE" ]; then
  cp "$SETTINGS_FILE" "$BACKUP_DIR/settings.json.bak"
  echo "Existing settings backed up to $BACKUP_DIR/settings.json.bak"
fi

# Update settings file with Augment optimizations
if [ -f "$SETTINGS_FILE" ]; then
  # Check if file is valid JSON
  if jq empty "$SETTINGS_FILE" 2>/dev/null; then
    # File is valid JSON, use jq to update it
    TMP_FILE=$(mktemp)
    jq '. + {
      "augment.timeout": 120000,
      "augment.experimental.features": false,
      "augment.webview.useServiceWorker": false,
      "augment.webview.useWebWorker": false,
      "augment.webview.useWebGL": false
    }' "$SETTINGS_FILE" > "$TMP_FILE"
    mv "$TMP_FILE" "$SETTINGS_FILE"
    echo "Updated settings with Augment optimizations"
  else
    # File is not valid JSON, create a new one
    echo "Existing settings file is not valid JSON. Creating a new one..."
    cat > "$SETTINGS_FILE" <<EOL
{
  "augment.timeout": 120000,
  "augment.experimental.features": false,
  "augment.webview.useServiceWorker": false,
  "augment.webview.useWebWorker": false,
  "augment.webview.useWebGL": false
}
EOL
    echo "Created new settings file with Augment optimizations"
  fi
else
  # File doesn't exist, create a new one
  cat > "$SETTINGS_FILE" <<EOL
{
  "augment.timeout": 120000,
  "augment.experimental.features": false,
  "augment.webview.useServiceWorker": false,
  "augment.webview.useWebWorker": false,
  "augment.webview.useWebGL": false
}
EOL
  echo "Created new settings file with Augment optimizations"
fi

echo ""
echo "Augment extension fix complete. Please restart VS Code."
echo ""
echo "If you continue to experience issues with the Augment extension:"
echo "1. Try uninstalling and reinstalling the extension"
echo "2. Contact Augment support with the error messages"
echo "3. Try using the extension with the --disable-gpu flag when launching VS Code"
