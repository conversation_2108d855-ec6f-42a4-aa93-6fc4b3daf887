#!/bin/bash

# run_with_poetry.sh - <PERSON>rip<PERSON> to run the DavarTruth application using Poetry
# This script ensures that the application runs with Poetry's managed environment
# which has all the required dependencies installed.

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}DavarTruth Application Runner (Poetry)${NC}"
echo "======================================="

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo -e "${RED}Error: Poetry is not installed!${NC}"
    echo "Would you like to install Poetry? (y/n)"
    read -r answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        curl -sSL https://install.python-poetry.org | python3 -
        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to install Poetry!${NC}"
            exit 1
        fi
        echo "Installing Poetry..."
        curl -sSL https://install.python-poetry.org | python3 -
        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to install Poetry. Please install it manually.${NC}"
            echo "Visit: https://python-poetry.org/docs/#installation"
            exit 1
        fi
        echo -e "${GREEN}Poetry installed successfully.${NC}"
    else
        echo "Please install Poetry manually and try again."
        echo "Visit: https://python-poetry.org/docs/#installation"
        exit 1
    fi
fi

# Check if dependencies are installed
echo "Checking dependencies..."
if ! poetry run python -c "import fastapi" &> /dev/null; then
    echo -e "${YELLOW}FastAPI not found. Installing dependencies...${NC}"
    poetry install
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to install dependencies. Please check your pyproject.toml file.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Dependencies installed successfully.${NC}"
else
    echo -e "${GREEN}FastAPI is already installed.${NC}"
fi

# Start the FastAPI application with uvicorn through Poetry
echo -e "${GREEN}Starting FastAPI application...${NC}"
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
