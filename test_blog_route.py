#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import get_async_db
from sqlalchemy import select, func
from app.models import BlogPost, User

async def test_blog_query():
    """Test the exact query used in the blog route"""
    
    async with get_async_db() as db:
        try:
            print("Testing blog route query...")
            
            # Test the exact query from the blog route
            query = select(BlogPost, User).outerjoin(User, BlogPost.user_id == User.id).where(
                (BlogPost.is_published == True) &
                (BlogPost.is_draft == False)
            ).order_by(BlogPost.created_at.desc())
            
            print(f"Query: {str(query)}")
            
            result = await db.execute(query)
            post_users = result.fetchall()
            
            print(f"Found {len(post_users)} posts")
            
            posts = []
            for post, author in post_users:
                print(f"Post: {post.title}")
                print(f"  ID: {post.id}")
                print(f"  is_published: {post.is_published}")
                print(f"  is_draft: {post.is_draft}")
                print(f"  is_scheduled: {getattr(post, 'is_scheduled', 'N/A')}")
                print(f"  Author: {author.username if author else 'None'}")
                print(f"  Created: {post.created_at}")
                print()
                
                posts.append({
                    "id": post.id,
                    "title": post.title,
                    "content": post.content,
                    "created_at": post.created_at,
                    "user_id": post.user_id,
                    "category": post.category,
                    "author": author
                })
            
            print(f"Final posts list has {len(posts)} items")
            
            # Test categories query
            print("\nTesting categories query...")
            category_query = select(func.distinct(BlogPost.category)).where(
                (BlogPost.is_published == True) &
                (BlogPost.is_draft == False) &
                (BlogPost.category != None)
            )
            category_result = await db.execute(category_query)
            categories = [cat[0] for cat in category_result if cat[0]]
            print(f"Categories: {categories}")
            
            return posts, categories
            
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
            return [], []

if __name__ == "__main__":
    posts, categories = asyncio.run(test_blog_query())
    print(f"\nFinal result: {len(posts)} posts, {len(categories)} categories")
